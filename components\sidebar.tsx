"use client"

import type React from "react"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import { UserProfile } from "@/components/user-profile"
import { BarChart3, Home, MessageSquare, Package, Search, Settings, AtSign } from "lucide-react"

interface SidebarProps extends React.HTMLAttributes<HTMLDivElement> { }

export function Sidebar({ className }: SidebarProps) {
  const pathname = usePathname()

  return (
    <div className={cn("pb-12 border-r h-screen overflow-y-auto", className)}>
      <div className="space-y-4 py-4">
        <div className="px-4 py-2">
          <h2 className="mb-2 px-2 text-xl font-semibold tracking-tight">Influenzy</h2>
          <div className="space-y-1">
            <Button
              asChild
              variant={pathname === "/brand/dashboard" ? "secondary" : "ghost"}
              className="w-full justify-start"
            >
              <Link href="/brand/dashboard">
                <Home className="mr-2 h-4 w-4" />
                Dashboard
              </Link>
            </Button>
            <Button
              asChild
              variant={pathname === "/brand/campaigns" ? "secondary" : "ghost"}
              className="w-full justify-start"
            >
              <Link href="/brand/campaigns">
                <Package className="mr-2 h-4 w-4" />
                Campaigns
              </Link>
            </Button>
            <Button
              asChild
              variant={pathname === "/brand/discovery" ? "secondary" : "ghost"}
              className="w-full justify-start"
            >
              <Link href="/brand/discovery">
                <Search className="mr-2 h-4 w-4" />
                Discover Creators
              </Link>
            </Button>
            <Button
              asChild
              variant={pathname === "/brand/analytics" ? "secondary" : "ghost"}
              className="w-full justify-start"
            >
              <Link href="/brand/analytics">
                <BarChart3 className="mr-2 h-4 w-4" />
                Analytics
              </Link>
            </Button>
            <Button
              asChild
              variant={pathname === "/brand/mentions" ? "secondary" : "ghost"}
              className="w-full justify-start"
            >
              <Link href="/brand/mentions">
                <AtSign className="mr-2 h-4 w-4" />
                Your Mentions
              </Link>
            </Button>
            <Button
              asChild
              variant={pathname === "/brand/messages" ? "secondary" : "ghost"}
              className="w-full justify-start"
            >
              <Link href="/brand/messages">
                <MessageSquare className="mr-2 h-4 w-4" />
                Messages
              </Link>
            </Button>
            <Button
              asChild
              variant={pathname === "/brand/settings" ? "secondary" : "ghost"}
              className="w-full justify-start"
            >
              <Link href="/brand/settings">
                <Settings className="mr-2 h-4 w-4" />
                Settings
              </Link>
            </Button>
          </div>
        </div>
      </div>
      <div className="mt-auto border-t">
        <UserProfile />
      </div>
    </div>
  )
}
