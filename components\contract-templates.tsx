"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { FileText, Download, Eye, Plus } from "lucide-react"

export function ContractTemplates() {
  const [activeTab, setActiveTab] = useState("all")

  // Mock data for contract templates
  const templates = [
    {
      id: 1,
      title: "Standard Influencer Agreement",
      description: "A general agreement for one-time collaborations with influencers.",
      category: "standard",
      lastUpdated: "2023-05-15",
    },
    {
      id: 2,
      title: "Long-term Ambassador Contract",
      description: "For ongoing partnerships with brand ambassadors.",
      category: "ambassador",
      lastUpdated: "2023-04-20",
    },
    {
      id: 3,
      title: "Product Review Agreement",
      description: "Specifically for product reviews and testimonials.",
      category: "review",
      lastUpdated: "2023-06-01",
    },
    {
      id: 4,
      title: "Content License Agreement",
      description: "For licensing and usage rights of influencer content.",
      category: "license",
      lastUpdated: "2023-05-10",
    },
    {
      id: 5,
      title: "Event Appearance Contract",
      description: "For in-person event appearances and promotions.",
      category: "event",
      lastUpdated: "2023-03-25",
    },
  ]

  const filteredTemplates = templates.filter((template) => {
    if (activeTab === "all") return true
    return template.category === activeTab
  })

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Contract Templates</CardTitle>
            <CardDescription>Standardized agreements for influencer collaborations</CardDescription>
          </div>
          <Dialog>
            <DialogTrigger asChild>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                New Template
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Create New Contract Template</DialogTitle>
                <DialogDescription>Create a custom contract template for your collaborations</DialogDescription>
              </DialogHeader>
              <div className="py-4">
                <p className="text-center text-muted-foreground">Contract template creator coming soon!</p>
              </div>
              <DialogFooter>
                <Button variant="outline">Cancel</Button>
                <Button>Create Template</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="all" onValueChange={setActiveTab}>
          <TabsList className="mb-4">
            <TabsTrigger value="all">All</TabsTrigger>
            <TabsTrigger value="standard">Standard</TabsTrigger>
            <TabsTrigger value="ambassador">Ambassador</TabsTrigger>
            <TabsTrigger value="review">Review</TabsTrigger>
            <TabsTrigger value="license">License</TabsTrigger>
            <TabsTrigger value="event">Event</TabsTrigger>
          </TabsList>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {filteredTemplates.map((template) => (
              <Card key={template.id}>
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg flex items-center">
                    <FileText className="mr-2 h-5 w-5 text-muted-foreground" />
                    {template.title}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground">{template.description}</p>
                  <p className="text-xs text-muted-foreground mt-2">
                    Last updated: {new Date(template.lastUpdated).toLocaleDateString()}
                  </p>
                </CardContent>
                <CardFooter className="flex justify-end gap-2">
                  <Button variant="outline" size="sm">
                    <Eye className="mr-2 h-4 w-4" />
                    Preview
                  </Button>
                  <Button size="sm">
                    <Download className="mr-2 h-4 w-4" />
                    Use Template
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>
        </Tabs>
      </CardContent>
    </Card>
  )
}
