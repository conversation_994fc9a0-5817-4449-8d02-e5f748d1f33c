import gsap from "gsap"
import { ScrollTrigger } from "gsap/ScrollTrigger"
import { ScrollToPlugin } from "gsap/ScrollToPlugin"

// Only register plugins once
let isRegistered = false

export const registerGSAP = () => {
  if (!isRegistered && typeof window !== "undefined") {
    gsap.registerPlugin(ScrollTrigger, ScrollToPlugin)
    isRegistered = true
  }
}

// Helper to create staggered animations
export const staggerElements = (
  elements: Element[] | NodeListOf<Element>,
  animationProps: gsap.TweenVars,
  staggerAmount = 0.1,
) => {
  return gsap.fromTo(
    elements,
    { opacity: 0, y: 30 },
    {
      opacity: 1,
      y: 0,
      stagger: staggerAmount,
      ease: "power3.out",
      ...animationProps,
    },
  )
}

// Helper to create reveal animations on scroll
export const createScrollTrigger = (
  trigger: Element,
  animation: gsap.core.Tween | gsap.core.Timeline,
  options: Partial<ScrollTrigger.Vars> = {},
) => {
  return ScrollTrigger.create({
    trigger,
    start: "top 80%",
    end: "bottom 20%",
    animation,
    toggleActions: "play none none reverse",
    ...options,
  })
}

// Clean up all ScrollTriggers
export const cleanupScrollTriggers = () => {
  if (typeof window !== "undefined") {
    ScrollTrigger.getAll().forEach((trigger) => trigger.kill())
  }
}
