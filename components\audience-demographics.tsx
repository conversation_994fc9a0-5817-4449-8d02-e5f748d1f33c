"use client"

import { <PERSON>, <PERSON><PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import {
  <PERSON>sponsive<PERSON>ontaine<PERSON>,
  <PERSON><PERSON>hart,
  Pie,
  Cell,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
} from "recharts"

interface AudienceDemographicsProps {
  compact?: boolean
}

export function AudienceDemographics({ compact = false }: AudienceDemographicsProps) {
  // Mock data for audience demographics
  const ageData = [
    { name: "13-17", value: 5 },
    { name: "18-24", value: 35 },
    { name: "25-34", value: 40 },
    { name: "35-44", value: 15 },
    { name: "45+", value: 5 },
  ]

  const genderData = [
    { name: "Female", value: 65 },
    { name: "Male", value: 32 },
    { name: "Other", value: 3 },
  ]

  const locationData = [
    { name: "United States", value: 40 },
    { name: "United Kingdom", value: 15 },
    { name: "Canada", value: 10 },
    { name: "Australia", value: 8 },
    { name: "Germany", value: 7 },
    { name: "France", value: 5 },
    { name: "Other", value: 15 },
  ]

  const interestData = [
    { name: "Fashion", value: 25 },
    { name: "Beauty", value: 20 },
    { name: "Fitness", value: 15 },
    { name: "Travel", value: 12 },
    { name: "Technology", value: 10 },
    { name: "Food", value: 8 },
    { name: "Other", value: 10 },
  ]

  const COLORS = ["#6C5CE7", "#0984E3", "#00CEC9", "#FF6B81", "#FDCB6E", "#E84393", "#D63031", "#636E72"]

  if (compact) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Audience Demographics</CardTitle>
          <CardDescription>Key audience insights</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h3 className="text-sm font-medium mb-2">Age Distribution</h3>
              <ResponsiveContainer width="100%" height={150}>
                <PieChart>
                  <Pie
                    data={ageData}
                    cx="50%"
                    cy="50%"
                    innerRadius={40}
                    outerRadius={60}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, value }) => `${name}: ${value}%`}
                  >
                    {ageData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                </PieChart>
              </ResponsiveContainer>
            </div>
            <div>
              <h3 className="text-sm font-medium mb-2">Gender Distribution</h3>
              <ResponsiveContainer width="100%" height={150}>
                <PieChart>
                  <Pie
                    data={genderData}
                    cx="50%"
                    cy="50%"
                    innerRadius={40}
                    outerRadius={60}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, value }) => `${name}: ${value}%`}
                  >
                    {genderData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                </PieChart>
              </ResponsiveContainer>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Audience Demographics</CardTitle>
        <CardDescription>Understand your audience composition</CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="age" className="space-y-4">
          <TabsList>
            <TabsTrigger value="age">Age</TabsTrigger>
            <TabsTrigger value="gender">Gender</TabsTrigger>
            <TabsTrigger value="location">Location</TabsTrigger>
            <TabsTrigger value="interests">Interests</TabsTrigger>
          </TabsList>
          <TabsContent value="age">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 h-[400px]">
              <div>
                <h3 className="text-sm font-medium mb-4">Age Distribution</h3>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={ageData}
                      cx="50%"
                      cy="50%"
                      innerRadius={60}
                      outerRadius={100}
                      fill="#8884d8"
                      dataKey="value"
                      label={({ name, value }) => `${name}: ${value}%`}
                    >
                      {ageData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value) => `${value}%`} />
                  </PieChart>
                </ResponsiveContainer>
              </div>
              <div>
                <h3 className="text-sm font-medium mb-4">Age Distribution by Platform</h3>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart
                    data={[
                      { name: "13-17", instagram: 8, youtube: 5, tiktok: 15, twitter: 2 },
                      { name: "18-24", instagram: 35, youtube: 30, tiktok: 45, twitter: 25 },
                      { name: "25-34", instagram: 40, youtube: 45, tiktok: 30, twitter: 40 },
                      { name: "35-44", instagram: 12, youtube: 15, tiktok: 8, twitter: 20 },
                      { name: "45+", instagram: 5, youtube: 5, tiktok: 2, twitter: 13 },
                    ]}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip formatter={(value) => `${value}%`} />
                    <Legend />
                    <Bar dataKey="instagram" fill="#6C5CE7" />
                    <Bar dataKey="youtube" fill="#0984E3" />
                    <Bar dataKey="tiktok" fill="#00CEC9" />
                    <Bar dataKey="twitter" fill="#FF6B81" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </div>
            <div className="mt-6">
              <h3 className="text-sm font-medium mb-2">Key Insights</h3>
              <ul className="list-disc pl-5 space-y-1 text-sm">
                <li>The majority of your audience (75%) is between 18-34 years old</li>
                <li>TikTok has the highest percentage of younger users (13-24)</li>
                <li>YouTube and Twitter have more users in the 25-44 age range</li>
                <li>
                  Consider targeting content specifically for the 25-34 demographic as they represent your largest
                  audience segment
                </li>
              </ul>
            </div>
          </TabsContent>
          <TabsContent value="gender">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 h-[400px]">
              <div>
                <h3 className="text-sm font-medium mb-4">Gender Distribution</h3>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={genderData}
                      cx="50%"
                      cy="50%"
                      innerRadius={60}
                      outerRadius={100}
                      fill="#8884d8"
                      dataKey="value"
                      label={({ name, value }) => `${name}: ${value}%`}
                    >
                      {genderData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value) => `${value}%`} />
                  </PieChart>
                </ResponsiveContainer>
              </div>
              <div>
                <h3 className="text-sm font-medium mb-4">Gender Distribution by Platform</h3>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart
                    data={[
                      { name: "Female", instagram: 70, youtube: 60, tiktok: 65, twitter: 55 },
                      { name: "Male", instagram: 28, youtube: 37, tiktok: 32, twitter: 40 },
                      { name: "Other", instagram: 2, youtube: 3, tiktok: 3, twitter: 5 },
                    ]}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip formatter={(value) => `${value}%`} />
                    <Legend />
                    <Bar dataKey="instagram" fill="#6C5CE7" />
                    <Bar dataKey="youtube" fill="#0984E3" />
                    <Bar dataKey="tiktok" fill="#00CEC9" />
                    <Bar dataKey="twitter" fill="#FF6B81" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </div>
            <div className="mt-6">
              <h3 className="text-sm font-medium mb-2">Key Insights</h3>
              <ul className="list-disc pl-5 space-y-1 text-sm">
                <li>Your audience is predominantly female (65%)</li>
                <li>Instagram has the highest percentage of female followers (70%)</li>
                <li>Twitter has a more balanced gender distribution compared to other platforms</li>
                <li>
                  Consider creating more content that appeals to your female audience while still maintaining engagement
                  with male followers
                </li>
              </ul>
            </div>
          </TabsContent>
          <TabsContent value="location">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 h-[400px]">
              <div>
                <h3 className="text-sm font-medium mb-4">Geographic Distribution</h3>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={locationData}
                      cx="50%"
                      cy="50%"
                      innerRadius={60}
                      outerRadius={100}
                      fill="#8884d8"
                      dataKey="value"
                      label={({ name, value }) => `${name}: ${value}%`}
                    >
                      {locationData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value) => `${value}%`} />
                  </PieChart>
                </ResponsiveContainer>
              </div>
              <div>
                <h3 className="text-sm font-medium mb-4">Top Cities</h3>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart
                    layout="vertical"
                    data={[
                      { name: "New York", value: 12 },
                      { name: "Los Angeles", value: 10 },
                      { name: "London", value: 8 },
                      { name: "Toronto", value: 6 },
                      { name: "Chicago", value: 5 },
                      { name: "Sydney", value: 4 },
                      { name: "Berlin", value: 3 },
                      { name: "Paris", value: 3 },
                    ]}
                    margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis type="number" />
                    <YAxis dataKey="name" type="category" />
                    <Tooltip formatter={(value) => `${value}%`} />
                    <Bar dataKey="value" fill="#6C5CE7" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </div>
            <div className="mt-6">
              <h3 className="text-sm font-medium mb-2">Key Insights</h3>
              <ul className="list-disc pl-5 space-y-1 text-sm">
                <li>Your audience is primarily based in English-speaking countries (73%)</li>
                <li>The United States represents your largest market (40%)</li>
                <li>Urban areas show the highest concentration of followers</li>
                <li>
                  Consider creating region-specific content for your top locations and scheduling posts to accommodate
                  different time zones
                </li>
              </ul>
            </div>
          </TabsContent>
          <TabsContent value="interests">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 h-[400px]">
              <div>
                <h3 className="text-sm font-medium mb-4">Interest Categories</h3>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={interestData}
                      cx="50%"
                      cy="50%"
                      innerRadius={60}
                      outerRadius={100}
                      fill="#8884d8"
                      dataKey="value"
                      label={({ name, value }) => `${name}: ${value}%`}
                    >
                      {interestData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value) => `${value}%`} />
                  </PieChart>
                </ResponsiveContainer>
              </div>
              <div>
                <h3 className="text-sm font-medium mb-4">Engagement by Interest</h3>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart
                    data={[
                      { name: "Fashion", engagement: 6.2 },
                      { name: "Beauty", engagement: 5.8 },
                      { name: "Fitness", engagement: 7.1 },
                      { name: "Travel", engagement: 6.5 },
                      { name: "Technology", engagement: 4.9 },
                      { name: "Food", engagement: 5.5 },
                    ]}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip formatter={(value) => `${value}%`} />
                    <Legend />
                    <Bar dataKey="engagement" name="Engagement Rate (%)" fill="#6C5CE7" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </div>
            <div className="mt-6">
              <h3 className="text-sm font-medium mb-2">Key Insights</h3>
              <ul className="list-disc pl-5 space-y-1 text-sm">
                <li>Fashion and Beauty are your audience's primary interests (45% combined)</li>
                <li>Fitness content has the highest engagement rate (7.1%)</li>
                <li>
                  Technology content has the lowest engagement rate but still represents 10% of your audience's
                  interests
                </li>
                <li>
                  Consider creating more fitness-related content to capitalize on high engagement rates while
                  maintaining your fashion and beauty focus
                </li>
              </ul>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
