"use client"

import type React from "react"

import { useEffect, useRef } from "react"
import { usePathname } from "next/navigation"
import gsap from "gsap"

export function PageTransition({ children }: { children: React.ReactNode }) {
  const pathname = usePathname()
  const pageRef = useRef<HTMLDivElement>(null)
  const overlayRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    // Create a timeline for the page transition
    const tl = gsap.timeline()

    // Animate the overlay
    tl.fromTo(
      overlayRef.current,
      { scaleY: 0, transformOrigin: "bottom" },
      { scaleY: 1, duration: 0.5, ease: "power2.inOut" },
    )
      .fromTo(pageRef.current, { opacity: 0, y: 20 }, { opacity: 1, y: 0, duration: 0.5, ease: "power2.out" })
      .to(overlayRef.current, { scaleY: 0, transformOrigin: "top", duration: 0.5, ease: "power2.inOut" }, "-=0.3")

    return () => {
      tl.kill()
    }
  }, [pathname])

  return (
    <div className="relative">
      <div ref={overlayRef} className="fixed inset-0 bg-background z-50 transform scale-y-0" />
      <div ref={pageRef} className="min-h-screen">
        {children}
      </div>
    </div>
  )
}
