"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Instagram, Twitter, Youtube, MessageCircle, Heart, Eye, ExternalLink } from "lucide-react"

interface SocialMediaMentionsProps {
  limit?: number
}

export function SocialMediaMentions({ limit }: SocialMediaMentionsProps) {
  const [platform, setPlatform] = useState("all")

  // Mock data for social media mentions
  const mentions = [
    {
      id: 1,
      platform: "instagram",
      type: "post",
      creator: {
        name: "<PERSON>",
        username: "@alex<PERSON><PERSON><PERSON>",
        avatar: "/placeholder.svg",
      },
      content: {
        image: "/placeholder.svg?height=400&width=400",
        caption: "Loving these new products from @FashionBrand! The quality is amazing. #sponsored",
      },
      engagement: {
        likes: 1250,
        comments: 78,
        views: 5600,
      },
      postedAt: "2023-07-10T14:30:00Z",
      url: "https://instagram.com/p/example1",
    },
    {
      id: 2,
      platform: "instagram",
      type: "story",
      creator: {
        name: "Sarah Miller",
        username: "@sarahmiller",
        avatar: "/placeholder.svg",
      },
      content: {
        image: "/placeholder.svg?height=400&width=400",
        caption: "Check out my new unboxing of @FashionBrand's summer collection! #fashion",
      },
      engagement: {
        views: 3200,
      },
      postedAt: "2023-07-12T09:15:00Z",
      url: "https://instagram.com/stories/example2",
    },
    {
      id: 3,
      platform: "tiktok",
      type: "video",
      creator: {
        name: "Mike Chen",
        username: "@mikechen",
        avatar: "/placeholder.svg",
      },
      content: {
        image: "/placeholder.svg?height=400&width=400",
        caption: "Trying on my new @FashionBrand outfit! What do you think? #fashion #ootd",
      },
      engagement: {
        likes: 4500,
        comments: 210,
        views: 18000,
      },
      postedAt: "2023-07-08T18:45:00Z",
      url: "https://tiktok.com/@mikechen/video/example3",
    },
    {
      id: 4,
      platform: "twitter",
      type: "tweet",
      creator: {
        name: "Tech Reviewer",
        username: "@techreviewer",
        avatar: "/placeholder.svg",
      },
      content: {
        image: "/placeholder.svg?height=400&width=400",
        caption: "Just got my hands on the new @FashionBrand accessories. Full review coming soon!",
      },
      engagement: {
        likes: 320,
        comments: 45,
        retweets: 87,
      },
      postedAt: "2023-07-11T11:20:00Z",
      url: "https://twitter.com/techreviewer/status/example4",
    },
    {
      id: 5,
      platform: "youtube",
      type: "video",
      creator: {
        name: "Fashion Channel",
        username: "@fashionchannel",
        avatar: "/placeholder.svg",
      },
      content: {
        image: "/placeholder.svg?height=400&width=400",
        caption: "Summer Fashion Haul 2023 featuring @FashionBrand | Try-On and Review",
      },
      engagement: {
        likes: 2800,
        comments: 320,
        views: 45000,
      },
      postedAt: "2023-07-05T15:00:00Z",
      url: "https://youtube.com/watch?v=example5",
    },
  ]

  const filteredMentions = mentions.filter((mention) => {
    if (platform === "all") return true
    return mention.platform === platform
  })

  const displayedMentions = limit ? filteredMentions.slice(0, limit) : filteredMentions

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return new Intl.DateTimeFormat("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
    }).format(date)
  }

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + "M"
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + "K"
    }
    return num.toString()
  }

  const getPlatformIcon = (platform: string) => {
    switch (platform) {
      case "instagram":
        return <Instagram className="h-4 w-4" />
      case "twitter":
        return <Twitter className="h-4 w-4" />
      case "youtube":
        return <Youtube className="h-4 w-4" />
      case "tiktok":
        return (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="h-4 w-4"
          >
            <path d="M9 12a4 4 0 1 0 0 8 4 4 0 0 0 0-8z" />
            <path d="M15 8a4 4 0 1 0 0-8 4 4 0 0 0 0 8z" />
            <path d="M15 8v8a4 4 0 0 1-4 4" />
            <line x1="15" y1="4" x2="15" y2="12" />
          </svg>
        )
      default:
        return null
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Social Media Mentions</CardTitle>
        <CardDescription>Track posts and stories where your brand was mentioned</CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="all" onValueChange={setPlatform}>
          <TabsList className="mb-4">
            <TabsTrigger value="all">All Platforms</TabsTrigger>
            <TabsTrigger value="instagram">Instagram</TabsTrigger>
            <TabsTrigger value="tiktok">TikTok</TabsTrigger>
            <TabsTrigger value="twitter">Twitter</TabsTrigger>
            <TabsTrigger value="youtube">YouTube</TabsTrigger>
          </TabsList>

          <div className="space-y-4">
            {displayedMentions.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">No mentions found</div>
            ) : (
              displayedMentions.map((mention) => (
                <div key={mention.id} className="flex flex-col gap-4 p-4 border rounded-lg">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <Avatar className="h-10 w-10">
                        <AvatarImage src={mention.creator.avatar} alt={mention.creator.name} />
                        <AvatarFallback>{mention.creator.name.substring(0, 2)}</AvatarFallback>
                      </Avatar>
                      <div>
                        <div className="font-medium">{mention.creator.name}</div>
                        <div className="text-sm text-muted-foreground">{mention.creator.username}</div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline" className="flex items-center gap-1">
                        {getPlatformIcon(mention.platform)}
                        <span className="capitalize">{mention.platform}</span>
                      </Badge>
                      <Badge variant="secondary">{mention.type}</Badge>
                    </div>
                  </div>

                  <Dialog>
                    <DialogTrigger asChild>
                      <div className="relative cursor-pointer group">
                        <div className="aspect-square w-full max-h-[200px] overflow-hidden rounded-md bg-muted flex items-center justify-center">
                          <img
                            src={mention.content.image}
                            alt="Social media content"
                            className="object-cover w-full h-full"
                          />
                        </div>
                        <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center rounded-md">
                          <Button variant="secondary" size="sm">
                            View Details
                          </Button>
                        </div>
                      </div>
                    </DialogTrigger>
                    <DialogContent className="max-w-2xl">
                      <DialogHeader>
                        <DialogTitle>Social Media Post Details</DialogTitle>
                        <DialogDescription>
                          Posted by {mention.creator.name} on {formatDate(mention.postedAt)}
                        </DialogDescription>
                      </DialogHeader>
                      <div className="grid gap-4 py-4">
                        <div className="flex items-center gap-4">
                          <Avatar className="h-12 w-12">
                            <AvatarImage src={mention.creator.avatar} alt={mention.creator.name} />
                            <AvatarFallback>{mention.creator.name.substring(0, 2)}</AvatarFallback>
                          </Avatar>
                          <div>
                            <h4 className="font-medium">{mention.creator.name}</h4>
                            <div className="text-sm text-muted-foreground">{mention.creator.username}</div>
                          </div>
                        </div>

                        <div className="aspect-square max-h-[400px] overflow-hidden rounded-md bg-muted flex items-center justify-center">
                          <img
                            src={mention.content.image}
                            alt="Social media content"
                            className="object-cover w-full h-full"
                          />
                        </div>

                        <div>
                          <p className="text-sm">{mention.content.caption}</p>
                        </div>

                        <div className="flex flex-wrap gap-4 text-sm">
                          {mention.engagement.likes && (
                            <div className="flex items-center">
                              <Heart className="h-4 w-4 mr-1 text-muted-foreground" />
                              {formatNumber(mention.engagement.likes)} likes
                            </div>
                          )}
                          {mention.engagement.comments && (
                            <div className="flex items-center">
                              <MessageCircle className="h-4 w-4 mr-1 text-muted-foreground" />
                              {formatNumber(mention.engagement.comments)} comments
                            </div>
                          )}
                          {mention.engagement.views && (
                            <div className="flex items-center">
                              <Eye className="h-4 w-4 mr-1 text-muted-foreground" />
                              {formatNumber(mention.engagement.views)} views
                            </div>
                          )}
                        </div>

                        <div className="flex justify-between items-center">
                          <div className="text-sm text-muted-foreground">
                            Posted on {formatDate(mention.postedAt)}
                          </div>
                          <Button variant="outline" size="sm" asChild>
                            <a href={mention.url} target="_blank" rel="noopener noreferrer" className="flex items-center">
                              <ExternalLink className="h-4 w-4 mr-1" />
                              View Original
                            </a>
                          </Button>
                        </div>
                      </div>
                    </DialogContent>
                  </Dialog>

                  <div>
                    <p className="text-sm line-clamp-2">{mention.content.caption}</p>
                  </div>

                  <div className="flex justify-between items-center">
                    <div className="flex flex-wrap gap-3 text-sm">
                      {mention.engagement.likes && (
                        <div className="flex items-center">
                          <Heart className="h-4 w-4 mr-1 text-muted-foreground" />
                          {formatNumber(mention.engagement.likes)}
                        </div>
                      )}
                      {mention.engagement.comments && (
                        <div className="flex items-center">
                          <MessageCircle className="h-4 w-4 mr-1 text-muted-foreground" />
                          {formatNumber(mention.engagement.comments)}
                        </div>
                      )}
                      {mention.engagement.views && (
                        <div className="flex items-center">
                          <Eye className="h-4 w-4 mr-1 text-muted-foreground" />
                          {formatNumber(mention.engagement.views)}
                        </div>
                      )}
                    </div>
                    <div className="text-xs text-muted-foreground">{formatDate(mention.postedAt)}</div>
                  </div>
                </div>
              ))
            )}
          </div>
        </Tabs>
      </CardContent>
    </Card>
  )
}
