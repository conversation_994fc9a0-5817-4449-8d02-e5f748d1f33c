"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Ta<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Calendar, DollarSign } from "lucide-react"

interface CollaborationRequestsProps {
  limit?: number
}

export function CollaborationRequests({ limit }: CollaborationRequestsProps) {
  const [activeTab, setActiveTab] = useState("pending")

  // Mock data for collaboration requests
  const requests = [
    {
      id: 1,
      brand: {
        name: "<PERSON><PERSON><PERSON>",
        logo: "/placeholder.svg",
      },
      status: "pending",
      type: "Sponsored Post",
      compensation: "$500",
      deadline: "2023-07-15",
      description:
        "We're looking for a fashion influencer to promote our new summer collection. The post should showcase our products in a natural setting.",
      requirements: "1 Instagram post, 2 stories",
      createdAt: "3 days ago",
    },
    {
      id: 2,
      brand: {
        name: "TechGadgets",
        logo: "/placeholder.svg",
      },
      status: "pending",
      type: "Product Review",
      compensation: "$300",
      deadline: "2023-07-20",
      description:
        "We'd like you to review our new wireless earbuds. Focus on sound quality, battery life, and comfort.",
      requirements: "1 YouTube video, 1 Instagram post",
      createdAt: "1 day ago",
    },
    {
      id: 3,
      brand: {
        name: "FitnessApp",
        logo: "/placeholder.svg",
      },
      status: "accepted",
      type: "App Promotion",
      compensation: "$400",
      deadline: "2023-07-25",
      description:
        "Promote our fitness app by showing how you use it in your workout routine. Highlight the tracking features.",
      requirements: "1 Instagram post, 3 stories",
      createdAt: "5 days ago",
    },
    {
      id: 4,
      brand: {
        name: "OrganicFood",
        logo: "/placeholder.svg",
      },
      status: "declined",
      type: "Recipe Creation",
      compensation: "$350",
      deadline: "2023-07-10",
      description: "Create a recipe using our organic ingredients and share it with your audience.",
      requirements: "1 Instagram post, 1 blog post",
      createdAt: "7 days ago",
    },
    {
      id: 5,
      brand: {
        name: "TravelAgency",
        logo: "/placeholder.svg",
      },
      status: "completed",
      type: "Destination Highlight",
      compensation: "$800",
      deadline: "2023-06-30",
      description: "Share your experience visiting one of our partner destinations. Focus on unique experiences.",
      requirements: "3 Instagram posts, 5 stories, 1 blog post",
      createdAt: "14 days ago",
    },
  ]

  const filteredRequests = requests.filter((request) => {
    if (activeTab === "all") return true
    return request.status === activeTab
  })

  const displayedRequests = limit ? filteredRequests.slice(0, limit) : filteredRequests

  return (
    <Card>
      <CardHeader>
        <CardTitle>Collaboration Requests</CardTitle>
        <CardDescription>Manage your incoming collaboration requests from brands</CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="pending" onValueChange={setActiveTab}>
          <TabsList className="mb-4">
            <TabsTrigger value="pending">Pending</TabsTrigger>
            <TabsTrigger value="accepted">Accepted</TabsTrigger>
            <TabsTrigger value="completed">Completed</TabsTrigger>
            <TabsTrigger value="declined">Declined</TabsTrigger>
            <TabsTrigger value="all">All</TabsTrigger>
          </TabsList>

          <div className="space-y-4">
            {displayedRequests.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">No {activeTab} requests found</div>
            ) : (
              displayedRequests.map((request) => (
                <div key={request.id} className="flex flex-col md:flex-row gap-4 p-4 border rounded-lg">
                  <div className="flex items-center gap-4">
                    <Avatar className="h-12 w-12">
                      <AvatarImage src={request.brand.logo || "/placeholder.svg"} alt={request.brand.name} />
                      <AvatarFallback>{request.brand.name.substring(0, 2)}</AvatarFallback>
                    </Avatar>
                    <div>
                      <h4 className="font-medium">{request.brand.name}</h4>
                      <p className="text-sm text-muted-foreground">{request.createdAt}</p>
                    </div>
                  </div>
                  <div className="flex-1 md:ml-4">
                    <div className="flex flex-wrap gap-2 mb-2">
                      <Badge variant="outline">{request.type}</Badge>
                      <Badge
                        variant={
                          request.status === "pending"
                            ? "outline"
                            : request.status === "accepted"
                              ? "secondary"
                              : request.status === "completed"
                                ? "default"
                                : "destructive"
                        }
                      >
                        {request.status.charAt(0).toUpperCase() + request.status.slice(1)}
                      </Badge>
                    </div>
                    <div className="flex flex-wrap gap-4 text-sm">
                      <div className="flex items-center">
                        <DollarSign className="h-4 w-4 mr-1 text-muted-foreground" />
                        {request.compensation}
                      </div>
                      <div className="flex items-center">
                        <Calendar className="h-4 w-4 mr-1 text-muted-foreground" />
                        Due: {new Date(request.deadline).toLocaleDateString()}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2 mt-4 md:mt-0">
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button variant="outline" size="sm">
                          View Details
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="max-w-2xl">
                        <DialogHeader>
                          <DialogTitle>Collaboration Request from {request.brand.name}</DialogTitle>
                          <DialogDescription>Review the details of this collaboration request</DialogDescription>
                        </DialogHeader>
                        <div className="grid gap-4 py-4">
                          <div className="flex items-center gap-4">
                            <Avatar className="h-12 w-12">
                              <AvatarImage src={request.brand.logo || "/placeholder.svg"} alt={request.brand.name} />
                              <AvatarFallback>{request.brand.name.substring(0, 2)}</AvatarFallback>
                            </Avatar>
                            <div>
                              <h4 className="font-medium">{request.brand.name}</h4>
                              <div className="flex gap-2 mt-1">
                                <Badge variant="outline">{request.type}</Badge>
                                <Badge
                                  variant={
                                    request.status === "pending"
                                      ? "outline"
                                      : request.status === "accepted"
                                        ? "secondary"
                                        : request.status === "completed"
                                          ? "default"
                                          : "destructive"
                                  }
                                >
                                  {request.status.charAt(0).toUpperCase() + request.status.slice(1)}
                                </Badge>
                              </div>
                            </div>
                          </div>

                          <div className="grid grid-cols-2 gap-4 mt-2">
                            <div>
                              <Label className="text-muted-foreground">Compensation</Label>
                              <p className="font-medium">{request.compensation}</p>
                            </div>
                            <div>
                              <Label className="text-muted-foreground">Deadline</Label>
                              <p className="font-medium">{new Date(request.deadline).toLocaleDateString()}</p>
                            </div>
                          </div>

                          <div>
                            <Label className="text-muted-foreground">Description</Label>
                            <p className="mt-1">{request.description}</p>
                          </div>

                          <div>
                            <Label className="text-muted-foreground">Requirements</Label>
                            <p className="mt-1">{request.requirements}</p>
                          </div>

                          {request.status === "pending" && (
                            <div>
                              <Label htmlFor="response">Your Response</Label>
                              <Textarea id="response" placeholder="Write a message to the brand..." className="mt-1" />
                            </div>
                          )}
                        </div>
                        <DialogFooter>
                          {request.status === "pending" && (
                            <>
                              <Button variant="outline">Decline</Button>
                              <Button>Accept Request</Button>
                            </>
                          )}
                          {request.status === "accepted" && <Button>Mark as Completed</Button>}
                          {request.status === "completed" && <Button variant="outline">View Details</Button>}
                          {request.status === "declined" && <Button variant="outline">View Details</Button>}
                        </DialogFooter>
                      </DialogContent>
                    </Dialog>

                    {request.status === "pending" && (
                      <>
                        <Button variant="outline" size="sm">
                          Decline
                        </Button>
                        <Button size="sm">Accept</Button>
                      </>
                    )}
                    {request.status === "accepted" && (
                      <Button variant="default" size="sm">
                        Message
                      </Button>
                    )}
                  </div>
                </div>
              ))
            )}
          </div>
        </Tabs>
      </CardContent>
      {limit && filteredRequests.length > limit && (
        <CardFooter>
          <Button variant="outline" className="w-full">
            View All Requests
          </Button>
        </CardFooter>
      )}
    </Card>
  )
}
