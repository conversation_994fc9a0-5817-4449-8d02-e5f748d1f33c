"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Slider } from "@/components/ui/slider"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import {
  Search,
  Filter,
  Instagram,
  Youtube,
  Twitter,
  Users,
  ThumbsUp,
  Heart,
  Bookmark,
  MessageSquare,
} from "lucide-react"
import { TikTok } from "@/components/tiktok"

export function InfluencerDiscovery() {
  const [searchTerm, setSearchTerm] = useState("")
  const [showFilters, setShowFilters] = useState(false)

  // Mock data for influencers
  const influencers = [
    {
      id: 1,
      name: "<PERSON> <PERSON>",
      username: "@alexjohnson",
      avatar: "/placeholder.svg",
      bio: "Lifestyle and fashion content creator. Sharing my daily outfits and travel adventures.",
      followers: "120K",
      engagement: "4.5%",
      platforms: ["instagram", "tiktok"],
      niches: ["Fashion", "Lifestyle", "Travel"],
      location: "New York, USA",
      averageLikes: "5.4K",
      averageComments: "320",
    },
    {
      id: 2,
      name: "Sarah Williams",
      username: "@sarahwilliams",
      avatar: "/placeholder.svg",
      bio: "Beauty and skincare enthusiast. Reviewing the latest products and sharing my skincare routine.",
      followers: "85K",
      engagement: "5.2%",
      platforms: ["instagram", "youtube"],
      niches: ["Beauty", "Skincare", "Lifestyle"],
      location: "Los Angeles, USA",
      averageLikes: "4.2K",
      averageComments: "280",
    },
    {
      id: 3,
      name: "Mike Chen",
      username: "@mikechen",
      avatar: "/placeholder.svg",
      bio: "Tech reviewer and gadget enthusiast. Unboxing the latest tech and providing honest reviews.",
      followers: "250K",
      engagement: "3.8%",
      platforms: ["youtube", "twitter"],
      niches: ["Technology", "Gaming", "Gadgets"],
      location: "San Francisco, USA",
      averageLikes: "9.5K",
      averageComments: "650",
    },
    {
      id: 4,
      name: "Emily Davis",
      username: "@emilydavis",
      avatar: "/placeholder.svg",
      bio: "Fitness trainer and nutrition coach. Sharing workout tips and healthy recipes.",
      followers: "95K",
      engagement: "6.1%",
      platforms: ["instagram", "tiktok"],
      niches: ["Fitness", "Health", "Nutrition"],
      location: "Miami, USA",
      averageLikes: "5.8K",
      averageComments: "420",
    },
    {
      id: 5,
      name: "David Kim",
      username: "@davidkim",
      avatar: "/placeholder.svg",
      bio: "Food blogger and home chef. Creating easy recipes and restaurant reviews.",
      followers: "75K",
      engagement: "4.9%",
      platforms: ["instagram", "youtube"],
      niches: ["Food", "Cooking", "Lifestyle"],
      location: "Chicago, USA",
      averageLikes: "3.7K",
      averageComments: "210",
    },
    {
      id: 6,
      name: "Jessica Miller",
      username: "@jessicamiller",
      avatar: "/placeholder.svg",
      bio: "Travel vlogger exploring the world. Sharing travel tips and destination guides.",
      followers: "180K",
      engagement: "4.2%",
      platforms: ["youtube", "instagram"],
      niches: ["Travel", "Adventure", "Photography"],
      location: "London, UK",
      averageLikes: "7.6K",
      averageComments: "520",
    },
  ]

  const filteredInfluencers = influencers.filter((influencer) => {
    if (!searchTerm) return true
    return (
      influencer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      influencer.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
      influencer.bio.toLowerCase().includes(searchTerm.toLowerCase()) ||
      influencer.niches.some((niche) => niche.toLowerCase().includes(searchTerm.toLowerCase()))
    )
  })

  const getPlatformIcon = (platform: string) => {
    switch (platform) {
      case "instagram":
        return <Instagram className="h-4 w-4" />
      case "youtube":
        return <Youtube className="h-4 w-4" />
      case "tiktok":
        return <TikTok className="h-4 w-4" />
      case "twitter":
        return <Twitter className="h-4 w-4" />
      default:
        return null
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Discover Influencers</CardTitle>
        <CardDescription>Find the perfect influencers for your campaigns</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col md:flex-row gap-4 mb-6">
          <div className="relative flex-1">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search by name, username, or niche..."
              className="pl-8"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <Button variant="outline" onClick={() => setShowFilters(!showFilters)}>
            <Filter className="mr-2 h-4 w-4" />
            Filters
          </Button>
        </div>

        {showFilters && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-muted/50 rounded-lg mb-6">
            <div>
              <Label className="mb-2 block">Platforms</Label>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Checkbox id="instagram" />
                  <label
                    htmlFor="instagram"
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    Instagram
                  </label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox id="youtube" />
                  <label
                    htmlFor="youtube"
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    YouTube
                  </label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox id="tiktok" />
                  <label
                    htmlFor="tiktok"
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    TikTok
                  </label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox id="twitter" />
                  <label
                    htmlFor="twitter"
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    Twitter
                  </label>
                </div>
              </div>
            </div>
            <div>
              <Label className="mb-2 block">Niche</Label>
              <Select>
                <SelectTrigger>
                  <SelectValue placeholder="Select niche" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="fashion">Fashion</SelectItem>
                  <SelectItem value="beauty">Beauty</SelectItem>
                  <SelectItem value="lifestyle">Lifestyle</SelectItem>
                  <SelectItem value="fitness">Fitness</SelectItem>
                  <SelectItem value="food">Food</SelectItem>
                  <SelectItem value="travel">Travel</SelectItem>
                  <SelectItem value="technology">Technology</SelectItem>
                  <SelectItem value="gaming">Gaming</SelectItem>
                </SelectContent>
              </Select>

              <Label className="mt-4 mb-2 block">Location</Label>
              <Select>
                <SelectTrigger>
                  <SelectValue placeholder="Select location" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="usa">United States</SelectItem>
                  <SelectItem value="uk">United Kingdom</SelectItem>
                  <SelectItem value="canada">Canada</SelectItem>
                  <SelectItem value="australia">Australia</SelectItem>
                  <SelectItem value="germany">Germany</SelectItem>
                  <SelectItem value="france">France</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label className="mb-2 block">Followers Range</Label>
              <div className="px-2">
                <Slider defaultValue={[0, 100]} max={100} step={1} />
                <div className="flex justify-between mt-1 text-xs text-muted-foreground">
                  <span>0</span>
                  <span>500K+</span>
                </div>
              </div>

              <Label className="mt-4 mb-2 block">Engagement Rate</Label>
              <div className="px-2">
                <Slider defaultValue={[0]} max={10} step={0.1} />
                <div className="flex justify-between mt-1 text-xs text-muted-foreground">
                  <span>0%</span>
                  <span>10%+</span>
                </div>
              </div>
            </div>
          </div>
        )}

        <div className="space-y-4">
          {filteredInfluencers.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">No influencers found</div>
          ) : (
            filteredInfluencers.map((influencer) => (
              <div key={influencer.id} className="border rounded-lg overflow-hidden">
                <div className="p-4">
                  <div className="flex flex-col md:flex-row gap-4">
                    <div className="flex items-start gap-4">
                      <Avatar className="h-12 w-12">
                        <AvatarImage src={influencer.avatar || "/placeholder.svg"} alt={influencer.name} />
                        <AvatarFallback>{influencer.name.substring(0, 2)}</AvatarFallback>
                      </Avatar>
                      <div>
                        <div className="flex items-center gap-2">
                          <h3 className="font-medium">{influencer.name}</h3>
                          <p className="text-sm text-muted-foreground">{influencer.username}</p>
                        </div>
                        <p className="text-sm mt-1 line-clamp-2">{influencer.bio}</p>
                        <div className="flex flex-wrap gap-2 mt-2">
                          {influencer.niches.map((niche) => (
                            <Badge key={niche} variant="outline">
                              {niche}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </div>
                    <div className="flex flex-wrap gap-4 md:ml-auto md:self-center">
                      <div className="flex items-center gap-1">
                        <Users className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm">{influencer.followers}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <ThumbsUp className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm">{influencer.engagement}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Heart className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm">{influencer.averageLikes}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <MessageSquare className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm">{influencer.averageComments}</span>
                      </div>
                    </div>
                  </div>

                  <div className="flex justify-between items-center mt-4">
                    <div className="flex gap-2">
                      {influencer.platforms.map((platform) => (
                        <div
                          key={platform}
                          className="flex items-center gap-1 text-xs text-muted-foreground bg-muted px-2 py-1 rounded-full"
                        >
                          {getPlatformIcon(platform)}
                          <span>{platform.charAt(0).toUpperCase() + platform.slice(1)}</span>
                        </div>
                      ))}
                    </div>
                    <div className="flex gap-2">
                      <Button variant="outline" size="sm">
                        <Bookmark className="mr-2 h-4 w-4" />
                        Save
                      </Button>
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button size="sm">Contact</Button>
                        </DialogTrigger>
                        <DialogContent>
                          <DialogHeader>
                            <DialogTitle>Contact {influencer.name}</DialogTitle>
                            <DialogDescription>Send a collaboration request to this influencer</DialogDescription>
                          </DialogHeader>
                          <div className="py-4">
                            <p className="text-center text-muted-foreground">Contact form coming soon!</p>
                          </div>
                          <DialogFooter>
                            <Button variant="outline">Cancel</Button>
                            <Button>Send Request</Button>
                          </DialogFooter>
                        </DialogContent>
                      </Dialog>
                    </div>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </CardContent>
    </Card>
  )
}
