"use client"

import { createContext, useContext, useState, useEffect, type ReactNode } from "react"

export type UserRole = "brand" | "creator" | null
export type SocialPlatform = "youtube" | "facebook" | "instagram" | "tiktok"

export interface User {
  id: string
  email: string
  name: string
  role: UserRole
  avatar?: string
  connectedPlatforms?: SocialPlatform[]
}

interface AuthContextType {
  user: User | null
  isLoading: boolean
  signUp: (email: string, password: string, name: string, role: UserRole) => Promise<void>
  signIn: (email: string, password: string) => Promise<void>
  signOut: () => void
  connectSocialPlatform: (platform: SocialPlatform) => void
  disconnectSocialPlatform: (platform: SocialPlatform) => void
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  // Check for saved user on initial load
  useEffect(() => {
    const savedUser = localStorage.getItem("influenzy_user")
    if (savedUser) {
      setUser(JSON.parse(savedUser))
    }
    setIsLoading(false)
  }, [])

  // Save user to localStorage when it changes
  useEffect(() => {
    if (user) {
      localStorage.setItem("influenzy_user", JSON.stringify(user))
    } else {
      localStorage.removeItem("influenzy_user")
    }
  }, [user])

  const signUp = async (email: string, password: string, name: string, role: UserRole) => {
    setIsLoading(true)
    try {
      // In a real app, this would be an API call
      // For this temporary auth system, we'll just create a user object
      const newUser: User = {
        id: Math.random().toString(36).substring(2, 9),
        email,
        name,
        role,
        connectedPlatforms: [],
      }

      // Simulate API delay
      await new Promise((resolve) => setTimeout(resolve, 1000))

      setUser(newUser)
    } finally {
      setIsLoading(false)
    }
  }

  const signIn = async (email: string, password: string) => {
    setIsLoading(true)
    try {
      // In a real app, this would be an API call
      // For this temporary auth system, we'll check localStorage for existing users
      const savedUsers = localStorage.getItem("influenzy_users")
      const users: User[] = savedUsers ? JSON.parse(savedUsers) : []

      // Simulate API delay
      await new Promise((resolve) => setTimeout(resolve, 1000))

      const foundUser = users.find((u) => u.email === email)

      if (foundUser) {
        setUser(foundUser)
      } else {
        throw new Error("Invalid credentials")
      }
    } finally {
      setIsLoading(false)
    }
  }

  const signOut = () => {
    setUser(null)
  }

  const connectSocialPlatform = (platform: SocialPlatform) => {
    if (user) {
      const connectedPlatforms = user.connectedPlatforms || []
      if (!connectedPlatforms.includes(platform)) {
        setUser({
          ...user,
          connectedPlatforms: [...connectedPlatforms, platform],
        })
      }
    }
  }

  const disconnectSocialPlatform = (platform: SocialPlatform) => {
    if (user && user.connectedPlatforms) {
      setUser({
        ...user,
        connectedPlatforms: user.connectedPlatforms.filter((p) => p !== platform),
      })
    }
  }

  return (
    <AuthContext.Provider
      value={{
        user,
        isLoading,
        signUp,
        signIn,
        signOut,
        connectSocialPlatform,
        disconnectSocialPlatform,
      }}
    >
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider")
  }
  return context
}
