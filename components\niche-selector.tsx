"use client"

import { useState } from "react"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Search, X } from "lucide-react"

const NICHES = [
  "Fashion",
  "Beauty",
  "Fitness",
  "Health",
  "Travel",
  "Food",
  "Lifestyle",
  "Technology",
  "Gaming",
  "Business",
  "Finance",
  "Education",
  "Entertainment",
  "Music",
  "Art",
  "Photography",
  "DIY",
  "Parenting",
  "Pets",
  "Sports",
  "Outdoors",
  "Home Decor",
  "Automotive",
  "Books",
  "Spirituality",
  "Wellness",
  "Sustainability",
  "Comedy",
  "Motivation",
  "Science",
]

export function NicheSelector() {
  const [selectedNiches, setSelectedNiches] = useState<string[]>([])
  const [searchTerm, setSearchTerm] = useState("")

  const filteredNiches = NICHES.filter(
    (niche) => niche.toLowerCase().includes(searchTerm.toLowerCase()) && !selectedNiches.includes(niche),
  )

  const handleSelectNiche = (niche: string) => {
    if (selectedNiches.length < 5 && !selectedNiches.includes(niche)) {
      setSelectedNiches([...selectedNiches, niche])
    }
  }

  const handleRemoveNiche = (niche: string) => {
    setSelectedNiches(selectedNiches.filter((n) => n !== niche))
  }

  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <div className="text-sm font-medium">Selected Niches ({selectedNiches.length}/5)</div>
        <div className="flex flex-wrap gap-2 min-h-12">
          {selectedNiches.length === 0 ? (
            <div className="text-sm text-muted-foreground">Select up to 5 niches that best describe your content</div>
          ) : (
            selectedNiches.map((niche) => (
              <Badge key={niche} variant="secondary" className="flex items-center gap-1">
                {niche}
                <button onClick={() => handleRemoveNiche(niche)} className="ml-1 rounded-full">
                  <X className="h-3 w-3" />
                  <span className="sr-only">Remove {niche}</span>
                </button>
              </Badge>
            ))
          )}
        </div>
      </div>

      <div className="space-y-2">
        <div className="relative">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search niches..."
            className="pl-8"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>

        <ScrollArea className="h-72 rounded-md border">
          <div className="p-4 grid grid-cols-2 gap-2">
            {filteredNiches.map((niche) => (
              <Badge
                key={niche}
                variant="outline"
                className="justify-center py-2 cursor-pointer hover:bg-secondary"
                onClick={() => handleSelectNiche(niche)}
              >
                {niche}
              </Badge>
            ))}
            {filteredNiches.length === 0 && (
              <div className="col-span-2 text-center py-8 text-muted-foreground">No niches found</div>
            )}
          </div>
        </ScrollArea>
      </div>
    </div>
  )
}
