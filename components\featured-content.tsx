"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Search, Filter, MoreHorizontal, Star, Plus, ArrowUp, ArrowDown, Eye, Edit, Trash2 } from "lucide-react"
import Image from "next/image"

export function FeaturedContent() {
  const [searchTerm, setSearchTerm] = useState("")
  const [activeTab, setActiveTab] = useState("influencers")

  // Mock data for featured content
  const featuredInfluencers = [
    {
      id: 1,
      name: "Alex Johnson",
      username: "@alexjohnson",
      avatar: "/placeholder.svg",
      bio: "Lifestyle and fashion content creator. Sharing my daily outfits and travel adventures.",
      followers: "120K",
      engagement: "4.5%",
      platforms: ["instagram", "tiktok"],
      niches: ["Fashion", "Lifestyle", "Travel"],
      featured: true,
      featuredOrder: 1,
      featuredReason: "Rising star with high engagement",
    },
    {
      id: 2,
      name: "Sarah Williams",
      username: "@sarahwilliams",
      avatar: "/placeholder.svg",
      bio: "Beauty and skincare enthusiast. Reviewing the latest products and sharing my skincare routine.",
      followers: "85K",
      engagement: "5.2%",
      platforms: ["instagram", "youtube"],
      niches: ["Beauty", "Skincare", "Lifestyle"],
      featured: true,
      featuredOrder: 2,
      featuredReason: "Beauty expert with authentic reviews",
    },
    {
      id: 3,
      name: "Mike Chen",
      username: "@mikechen",
      avatar: "/placeholder.svg",
      bio: "Tech reviewer and gadget enthusiast. Unboxing the latest tech and providing honest reviews.",
      followers: "250K",
      engagement: "3.8%",
      platforms: ["youtube", "twitter"],
      niches: ["Technology", "Gaming", "Gadgets"],
      featured: true,
      featuredOrder: 3,
      featuredReason: "Top tech influencer with large audience",
    },
  ]

  const featuredBrands = [
    {
      id: 1,
      name: "TechGadgets",
      logo: "/placeholder.svg",
      description: "Innovative tech products for everyday use.",
      industry: "Technology",
      campaigns: 12,
      featured: true,
      featuredOrder: 1,
      featuredReason: "Innovative brand with successful campaigns",
    },
    {
      id: 2,
      name: "FashionBrand",
      logo: "/placeholder.svg",
      description: "Trendy and sustainable fashion for all.",
      industry: "Fashion",
      campaigns: 8,
      featured: true,
      featuredOrder: 2,
      featuredReason: "Sustainable fashion leader",
    },
    {
      id: 3,
      name: "BeautyCompany",
      logo: "/placeholder.svg",
      description: "Clean and effective beauty products.",
      industry: "Beauty",
      campaigns: 10,
      featured: true,
      featuredOrder: 3,
      featuredReason: "Clean beauty innovator",
    },
  ]

  const featuredCampaigns = [
    {
      id: 1,
      title: "Summer Collection Launch",
      brand: {
        name: "FashionBrand",
        logo: "/placeholder.svg",
      },
      preview: "/placeholder.svg?height=400&width=400",
      description: "Showcasing our new summer collection with top fashion influencers.",
      influencers: 5,
      impressions: "850K",
      engagement: "4.8%",
      featured: true,
      featuredOrder: 1,
      featuredReason: "Highly successful campaign with excellent results",
    },
    {
      id: 2,
      title: "Tech Gadget Review",
      brand: {
        name: "TechGadgets",
        logo: "/placeholder.svg",
      },
      preview: "/placeholder.svg?height=400&width=400",
      description: "In-depth reviews of our latest tech gadget by tech experts.",
      influencers: 3,
      impressions: "500K",
      engagement: "5.2%",
      featured: true,
      featuredOrder: 2,
      featuredReason: "Innovative product with great engagement",
    },
    {
      id: 3,
      title: "Skincare Routine",
      brand: {
        name: "BeautyCompany",
        logo: "/placeholder.svg",
      },
      preview: "/placeholder.svg?height=400&width=400",
      description: "Beauty influencers sharing their skincare routines using our products.",
      influencers: 4,
      impressions: "620K",
      engagement: "6.1%",
      featured: true,
      featuredOrder: 3,
      featuredReason: "High engagement beauty campaign",
    },
  ]

  // Get the appropriate data based on the active tab
  const getActiveData = () => {
    switch (activeTab) {
      case "influencers":
        return featuredInfluencers
      case "brands":
        return featuredBrands
      case "campaigns":
        return featuredCampaigns
      default:
        return []
    }
  }

  const filteredData = getActiveData().filter((item) => {
    if (!searchTerm) return true

    if (activeTab === "influencers") {
      return (
        item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.bio.toLowerCase().includes(searchTerm.toLowerCase())
      )
    } else if (activeTab === "brands") {
      return (
        item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.industry.toLowerCase().includes(searchTerm.toLowerCase())
      )
    } else if (activeTab === "campaigns") {
      return (
        item.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.brand.name.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    return true
  })

  return (
    <Card>
      <CardHeader>
        <CardTitle>Featured Content</CardTitle>
        <CardDescription>Manage featured influencers, brands, and campaigns</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col md:flex-row gap-4 mb-6">
          <div className="relative flex-1">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search..."
              className="pl-8"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <div className="flex gap-2">
            <Button variant="outline" size="icon">
              <Filter className="h-4 w-4" />
            </Button>
            <Dialog>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="mr-2 h-4 w-4" />
                  Add Featured
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Add Featured Content</DialogTitle>
                  <DialogDescription>Feature new influencers, brands, or campaigns</DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                  <div>
                    <label className="text-sm font-medium">Content Type</label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="Select content type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="influencer">Influencer</SelectItem>
                        <SelectItem value="brand">Brand</SelectItem>
                        <SelectItem value="campaign">Campaign</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Search</label>
                    <div className="relative">
                      <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                      <Input placeholder="Search by name..." className="pl-8" />
                    </div>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Featured Reason</label>
                    <Input placeholder="Why is this content being featured?" />
                  </div>
                  <div>
                    <label className="text-sm font-medium">Display Order</label>
                    <Input type="number" min="1" placeholder="Display order (1 = highest)" />
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="homepage" />
                    <label
                      htmlFor="homepage"
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      Feature on homepage
                    </label>
                  </div>
                </div>
                <DialogFooter>
                  <Button variant="outline">Cancel</Button>
                  <Button>Add Featured</Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        <Tabs defaultValue="influencers" onValueChange={setActiveTab}>
          <TabsList className="mb-4">
            <TabsTrigger value="influencers">Featured Influencers</TabsTrigger>
            <TabsTrigger value="brands">Featured Brands</TabsTrigger>
            <TabsTrigger value="campaigns">Featured Campaigns</TabsTrigger>
          </TabsList>

          <TabsContent value="influencers">
            <div className="space-y-4">
              {filteredData.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">No featured influencers found</div>
              ) : (
                filteredData.map((influencer: any) => (
                  <Card key={influencer.id}>
                    <CardContent className="p-4">
                      <div className="flex flex-col md:flex-row gap-4">
                        <div className="md:w-1/4">
                          <div className="flex flex-col items-center">
                            <Avatar className="h-20 w-20 mb-2">
                              <AvatarImage src={influencer.avatar || "/placeholder.svg"} alt={influencer.name} />
                              <AvatarFallback>{influencer.name.substring(0, 2)}</AvatarFallback>
                            </Avatar>
                            <div className="text-center">
                              <div className="font-medium">{influencer.name}</div>
                              <div className="text-sm text-muted-foreground">{influencer.username}</div>
                            </div>
                          </div>
                        </div>
                        <div className="md:w-3/4">
                          <div className="flex justify-between items-start mb-2">
                            <div className="flex items-center gap-2">
                              <Badge variant="default" className="bg-brand-purple">
                                <Star className="mr-1 h-3 w-3" />
                                Featured #{influencer.featuredOrder}
                              </Badge>
                              <Badge variant="outline">{influencer.followers} Followers</Badge>
                              <Badge variant="outline">{influencer.engagement} Engagement</Badge>
                            </div>
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="icon">
                                  <MoreHorizontal className="h-4 w-4" />
                                  <span className="sr-only">Actions</span>
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                <DropdownMenuItem>
                                  <Eye className="mr-2 h-4 w-4" />
                                  View Profile
                                </DropdownMenuItem>
                                <DropdownMenuItem>
                                  <Edit className="mr-2 h-4 w-4" />
                                  Edit Featured
                                </DropdownMenuItem>
                                <DropdownMenuItem>
                                  <ArrowUp className="mr-2 h-4 w-4" />
                                  Move Up
                                </DropdownMenuItem>
                                <DropdownMenuItem>
                                  <ArrowDown className="mr-2 h-4 w-4" />
                                  Move Down
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem className="text-destructive">
                                  <Trash2 className="mr-2 h-4 w-4" />
                                  Remove Featured
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                          <p className="text-sm mb-2">{influencer.bio}</p>
                          <div className="flex flex-wrap gap-2 mb-2">
                            {influencer.niches.map((niche: string) => (
                              <Badge key={niche} variant="outline">
                                {niche}
                              </Badge>
                            ))}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            <span className="font-medium">Featured reason:</span> {influencer.featuredReason}
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))
              )}
            </div>
          </TabsContent>

          <TabsContent value="brands">
            <div className="space-y-4">
              {filteredData.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">No featured brands found</div>
              ) : (
                filteredData.map((brand: any) => (
                  <Card key={brand.id}>
                    <CardContent className="p-4">
                      <div className="flex flex-col md:flex-row gap-4">
                        <div className="md:w-1/4">
                          <div className="flex flex-col items-center">
                            <Avatar className="h-20 w-20 mb-2">
                              <AvatarImage src={brand.logo || "/placeholder.svg"} alt={brand.name} />
                              <AvatarFallback>{brand.name.substring(0, 2)}</AvatarFallback>
                            </Avatar>
                            <div className="text-center">
                              <div className="font-medium">{brand.name}</div>
                              <div className="text-sm text-muted-foreground">{brand.industry}</div>
                            </div>
                          </div>
                        </div>
                        <div className="md:w-3/4">
                          <div className="flex justify-between items-start mb-2">
                            <div className="flex items-center gap-2">
                              <Badge variant="default" className="bg-brand-blue">
                                <Star className="mr-1 h-3 w-3" />
                                Featured #{brand.featuredOrder}
                              </Badge>
                              <Badge variant="outline">{brand.campaigns} Campaigns</Badge>
                            </div>
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="icon">
                                  <MoreHorizontal className="h-4 w-4" />
                                  <span className="sr-only">Actions</span>
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                <DropdownMenuItem>
                                  <Eye className="mr-2 h-4 w-4" />
                                  View Profile
                                </DropdownMenuItem>
                                <DropdownMenuItem>
                                  <Edit className="mr-2 h-4 w-4" />
                                  Edit Featured
                                </DropdownMenuItem>
                                <DropdownMenuItem>
                                  <ArrowUp className="mr-2 h-4 w-4" />
                                  Move Up
                                </DropdownMenuItem>
                                <DropdownMenuItem>
                                  <ArrowDown className="mr-2 h-4 w-4" />
                                  Move Down
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem className="text-destructive">
                                  <Trash2 className="mr-2 h-4 w-4" />
                                  Remove Featured
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                          <p className="text-sm mb-2">{brand.description}</p>
                          <div className="text-sm text-muted-foreground">
                            <span className="font-medium">Featured reason:</span> {brand.featuredReason}
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))
              )}
            </div>
          </TabsContent>

          <TabsContent value="campaigns">
            <div className="space-y-4">
              {filteredData.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">No featured campaigns found</div>
              ) : (
                filteredData.map((campaign: any) => (
                  <Card key={campaign.id}>
                    <CardContent className="p-4">
                      <div className="flex flex-col md:flex-row gap-4">
                        <div className="md:w-1/3">
                          <div className="relative aspect-video rounded-md overflow-hidden">
                            <Image
                              src={campaign.preview || "/placeholder.svg"}
                              alt={campaign.title}
                              fill
                              className="object-cover"
                            />
                          </div>
                        </div>
                        <div className="md:w-2/3">
                          <div className="flex justify-between items-start mb-2">
                            <div>
                              <h3 className="font-medium text-lg">{campaign.title}</h3>
                              <div className="flex items-center gap-2 mt-1">
                                <Avatar className="h-6 w-6">
                                  <AvatarImage
                                    src={campaign.brand.logo || "/placeholder.svg"}
                                    alt={campaign.brand.name}
                                  />
                                  <AvatarFallback>{campaign.brand.name.substring(0, 2)}</AvatarFallback>
                                </Avatar>
                                <span className="text-sm">{campaign.brand.name}</span>
                              </div>
                            </div>
                            <div className="flex items-center gap-2">
                              <Badge variant="default" className="bg-brand-accent-cyan">
                                <Star className="mr-1 h-3 w-3" />
                                Featured #{campaign.featuredOrder}
                              </Badge>
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" size="icon">
                                    <MoreHorizontal className="h-4 w-4" />
                                    <span className="sr-only">Actions</span>
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                  <DropdownMenuItem>
                                    <Eye className="mr-2 h-4 w-4" />
                                    View Campaign
                                  </DropdownMenuItem>
                                  <DropdownMenuItem>
                                    <Edit className="mr-2 h-4 w-4" />
                                    Edit Featured
                                  </DropdownMenuItem>
                                  <DropdownMenuItem>
                                    <ArrowUp className="mr-2 h-4 w-4" />
                                    Move Up
                                  </DropdownMenuItem>
                                  <DropdownMenuItem>
                                    <ArrowDown className="mr-2 h-4 w-4" />
                                    Move Down
                                  </DropdownMenuItem>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem className="text-destructive">
                                    <Trash2 className="mr-2 h-4 w-4" />
                                    Remove Featured
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </div>
                          </div>
                          <p className="text-sm mb-2">{campaign.description}</p>
                          <div className="grid grid-cols-3 gap-2 mb-2">
                            <div className="text-sm">
                              <span className="text-muted-foreground">Influencers:</span> {campaign.influencers}
                            </div>
                            <div className="text-sm">
                              <span className="text-muted-foreground">Impressions:</span> {campaign.impressions}
                            </div>
                            <div className="text-sm">
                              <span className="text-muted-foreground">Engagement:</span> {campaign.engagement}
                            </div>
                          </div>
                          <div className="text-sm text-muted-foreground">
                            <span className="font-medium">Featured reason:</span> {campaign.featuredReason}
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))
              )}
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
