"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { SocialConnector } from "@/components/social-connector"
import { ProfileForm } from "@/components/profile-form"
import { NicheSelector } from "@/components/niche-selector"
import { Progress } from "@/components/ui/progress"
import { CheckCircle2 } from "lucide-react"

export function InfluencerOnboarding() {
  const router = useRouter()
  const [step, setStep] = useState(1)
  const totalSteps = 4
  const progress = (step / totalSteps) * 100

  const handleNext = () => {
    if (step < totalSteps) {
      setStep(step + 1)
    } else {
      router.push("/influencer/dashboard")
    }
  }

  const handleBack = () => {
    if (step > 1) {
      setStep(step - 1)
    }
  }

  return (
    <div className="container max-w-4xl py-10">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Set Up Your Influencer Profile</h1>
        <p className="text-muted-foreground">Complete these steps to get started on Influenzy</p>
        <div className="mt-6">
          <Progress value={progress} className="h-2" />
          <div className="flex justify-between mt-2 text-sm text-muted-foreground">
            <span>Getting Started</span>
            <span>Complete Profile</span>
          </div>
        </div>
      </div>

      {step === 1 && (
        <Card>
          <CardHeader>
            <CardTitle>Connect Your Social Accounts</CardTitle>
            <CardDescription>
              Link your social media accounts to automatically import your stats and content
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <SocialConnector platform="instagram" />
            <SocialConnector platform="youtube" />
            <SocialConnector platform="tiktok" />
            <SocialConnector platform="twitter" />
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button variant="ghost" onClick={handleBack} disabled={step === 1}>
              Back
            </Button>
            <Button onClick={handleNext}>Continue</Button>
          </CardFooter>
        </Card>
      )}

      {step === 2 && (
        <Card>
          <CardHeader>
            <CardTitle>Basic Information</CardTitle>
            <CardDescription>Tell us about yourself and your content</CardDescription>
          </CardHeader>
          <CardContent>
            <ProfileForm />
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button variant="ghost" onClick={handleBack}>
              Back
            </Button>
            <Button onClick={handleNext}>Continue</Button>
          </CardFooter>
        </Card>
      )}

      {step === 3 && (
        <Card>
          <CardHeader>
            <CardTitle>Select Your Niches</CardTitle>
            <CardDescription>Choose the categories that best describe your content</CardDescription>
          </CardHeader>
          <CardContent>
            <NicheSelector />
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button variant="ghost" onClick={handleBack}>
              Back
            </Button>
            <Button onClick={handleNext}>Continue</Button>
          </CardFooter>
        </Card>
      )}

      {step === 4 && (
        <Card>
          <CardHeader>
            <CardTitle>You're All Set!</CardTitle>
            <CardDescription>Your profile is ready to go</CardDescription>
          </CardHeader>
          <CardContent className="flex flex-col items-center py-10">
            <CheckCircle2 className="h-16 w-16 text-green-500 mb-4" />
            <h3 className="text-xl font-medium mb-2">Profile Successfully Created</h3>
            <p className="text-center text-muted-foreground mb-6">
              Your profile is now live and visible to brands looking for collaborations. You can edit your profile
              anytime from your dashboard.
            </p>
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button variant="ghost" onClick={handleBack}>
              Back
            </Button>
            <Button onClick={handleNext}>Go to Dashboard</Button>
          </CardFooter>
        </Card>
      )}
    </div>
  )
}
