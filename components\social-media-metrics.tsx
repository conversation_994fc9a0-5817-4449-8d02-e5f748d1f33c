"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"
import { Responsive<PERSON><PERSON><PERSON>, <PERSON><PERSON>hart, Line, BarChart, Bar, XAxis, YAxis, CartesianGrid, Legend } from "recharts"
import { Instagram, Twitter, Youtube, TrendingUp, Users, Eye, ThumbsUp } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

// Sample data - in a real app, this would come from an API
const socialData = {
  instagram: [
    { date: "Jan", followers: 12400, engagement: 3.2, reach: 45000, likes: 8900 },
    { date: "Feb", followers: 12800, engagement: 3.5, reach: 48000, likes: 9200 },
    { date: "Mar", followers: 13200, engagement: 3.8, reach: 52000, likes: 9800 },
    { date: "Apr", followers: 13900, engagement: 4.1, reach: 58000, likes: 10500 },
    { date: "May", followers: 14500, engagement: 4.3, reach: 62000, likes: 11200 },
    { date: "Jun", followers: 15200, engagement: 4.5, reach: 68000, likes: 12100 },
  ],
  tiktok: [
    { date: "Jan", followers: 18500, engagement: 5.8, reach: 120000, likes: 22000 },
    { date: "Feb", followers: 19800, engagement: 6.2, reach: 135000, likes: 24500 },
    { date: "Mar", followers: 21500, engagement: 6.5, reach: 150000, likes: 27000 },
    { date: "Apr", followers: 24000, engagement: 6.8, reach: 180000, likes: 32000 },
    { date: "May", followers: 27500, engagement: 7.1, reach: 210000, likes: 38000 },
    { date: "Jun", followers: 32000, engagement: 7.4, reach: 250000, likes: 45000 },
  ],
  youtube: [
    { date: "Jan", followers: 8200, engagement: 2.8, reach: 35000, likes: 4200 },
    { date: "Feb", followers: 8500, engagement: 2.9, reach: 37000, likes: 4500 },
    { date: "Mar", followers: 8800, engagement: 3.0, reach: 39000, likes: 4800 },
    { date: "Apr", followers: 9200, engagement: 3.1, reach: 42000, likes: 5200 },
    { date: "May", followers: 9600, engagement: 3.2, reach: 45000, likes: 5600 },
    { date: "Jun", followers: 10000, engagement: 3.3, reach: 48000, likes: 6000 },
  ],
  twitter: [
    { date: "Jan", followers: 5600, engagement: 1.8, reach: 22000, likes: 3100 },
    { date: "Feb", followers: 5800, engagement: 1.9, reach: 23000, likes: 3300 },
    { date: "Mar", followers: 6000, engagement: 2.0, reach: 24000, likes: 3500 },
    { date: "Apr", followers: 6300, engagement: 2.1, reach: 26000, likes: 3800 },
    { date: "May", followers: 6600, engagement: 2.2, reach: 28000, likes: 4100 },
    { date: "Jun", followers: 7000, engagement: 2.3, reach: 30000, likes: 4500 },
  ],
}

interface SocialMediaMetricsProps {
  dateRange?: { from: Date; to: Date }
}

export function SocialMediaMetrics({ dateRange }: SocialMediaMetricsProps) {
  const [platform, setPlatform] = useState("instagram")
  const [metric, setMetric] = useState("followers")

  const platformIcons = {
    instagram: <Instagram className="h-5 w-5" />,
    tiktok: (
      <svg className="h-5 w-5" viewBox="0 0 24 24" fill="currentColor">
        <path d="M19.321 5.562a5.122 5.122 0 0 1-.443-.258 6.228 6.228 0 0 1-1.137-.966c-1.345-1.481-1.209-3.328-1.194-3.453l.008-.065-.053-.036a.5.5 0 0 0-.133-.058L12.06.514a.5.5 0 0 0-.591.283l-.004.01c-.01.02-.251.508-.251 1.003v.139c0 .939.167 1.62.323 2.055.195.54.479.947.716 1.224a3.73 3.73 0 0 0 .382.355l.057.051c.018.016.04.036.062.057l.028.028-.122-.018a7.928 7.928 0 0 1-3.606-.906A8.056 8.056 0 0 1 6.26 3.002l-.068-.057-.042.079a6.276 6.276 0 0 0-.662 1.195 6.22 6.22 0 0 0-.491 2.494c.003 3.43 2.795 6.216 6.227 6.216h.027v4.8c0 .379-.229.558-.352.618-.158.077-.381.079-.498.061l-.05-.007-.023.045a5.7 5.7 0 0 1-2.736 2.682 5.7 5.7 0 0 1-4.366-.133.5.5 0 0 0-.722.451v4.032a.5.5 0 0 0 .001.053c0 .011-.004.022-.001.033a.5.5 0 0 0 .7.027c.309.324.68.609 1.102.848a8.288 8.288 0 0 0 4.122.956c4.626-.096 8.343-3.905 8.343-8.533v-9.927c0-.037.009-.072.024-.104.245.24.518.456.816.639a6.264 6.264 0 0 0 3.164.866h.026a.5.5 0 0 0 .5-.5V6.062a.5.5 0 0 0-.5-.5h-.026z" />
      </svg>
    ),
    youtube: <Youtube className="h-5 w-5" />,
    twitter: <Twitter className="h-5 w-5" />,
  }

  const metricIcons = {
    followers: <Users className="h-5 w-5" />,
    engagement: <TrendingUp className="h-5 w-5" />,
    reach: <Eye className="h-5 w-5" />,
    likes: <ThumbsUp className="h-5 w-5" />,
  }

  const data = socialData[platform as keyof typeof socialData]

  const getMetricValue = (platform: string, metric: string) => {
    const latestData =
      socialData[platform as keyof typeof socialData][socialData[platform as keyof typeof socialData].length - 1]
    const previousData =
      socialData[platform as keyof typeof socialData][socialData[platform as keyof typeof socialData].length - 2]

    const current = latestData[metric as keyof typeof latestData]
    const previous = previousData[metric as keyof typeof previousData]
    const percentChange = ((current - previous) / previous) * 100

    return {
      value: current,
      percentChange: percentChange.toFixed(1),
    }
  }

  return (
    <div className="space-y-4">
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div>
          <h2 className="text-2xl font-bold">Social Media Metrics</h2>
          <p className="text-muted-foreground">Track your performance across social platforms</p>
        </div>
        <div className="flex gap-2">
          <Select value={platform} onValueChange={setPlatform}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select platform" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="instagram">Instagram</SelectItem>
              <SelectItem value="tiktok">TikTok</SelectItem>
              <SelectItem value="youtube">YouTube</SelectItem>
              <SelectItem value="twitter">Twitter</SelectItem>
            </SelectContent>
          </Select>
          <Select value={metric} onValueChange={setMetric}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select metric" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="followers">Followers</SelectItem>
              <SelectItem value="engagement">Engagement Rate</SelectItem>
              <SelectItem value="reach">Reach</SelectItem>
              <SelectItem value="likes">Likes</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {Object.keys(platformIcons).map((p) => (
          <Card key={p} className={p === platform ? "border-primary" : ""}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium flex items-center gap-2">
                {platformIcons[p as keyof typeof platformIcons]}
                {p.charAt(0).toUpperCase() + p.slice(1)}
              </CardTitle>
              <Badge variant={Number(getMetricValue(p, metric).percentChange) > 0 ? "default" : "destructive"}>
                {Number(getMetricValue(p, metric).percentChange) > 0 ? "+" : ""}
                {getMetricValue(p, metric).percentChange}%
              </Badge>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {metric === "engagement"
                  ? `${getMetricValue(p, metric).value}%`
                  : getMetricValue(p, metric).value.toLocaleString()}
              </div>
              <p className="text-xs text-muted-foreground">{metric.charAt(0).toUpperCase() + metric.slice(1)}</p>
            </CardContent>
          </Card>
        ))}
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            {platformIcons[platform as keyof typeof platformIcons]}
            {platform.charAt(0).toUpperCase() + platform.slice(1)} {metric.charAt(0).toUpperCase() + metric.slice(1)}{" "}
            Trend
          </CardTitle>
          <CardDescription>
            {dateRange
              ? `${dateRange.from.toLocaleDateString()} - ${dateRange.to.toLocaleDateString()}`
              : "Last 6 months"}
          </CardDescription>
        </CardHeader>
        <CardContent className="h-[400px]">
          <ChartContainer
            config={{
              [metric]: {
                label: metric.charAt(0).toUpperCase() + metric.slice(1),
                color: "hsl(var(--chart-1))",
              },
            }}
            className="h-full"
          >
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={data}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <ChartTooltip content={<ChartTooltipContent />} />
                <Legend />
                <Line type="monotone" dataKey={metric} stroke="var(--color-followers)" activeDot={{ r: 8 }} />
              </LineChart>
            </ResponsiveContainer>
          </ChartContainer>
        </CardContent>
      </Card>

      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Platform Comparison</CardTitle>
            <CardDescription>Comparing {metric} across platforms</CardDescription>
          </CardHeader>
          <CardContent className="h-[300px]">
            <ChartContainer
              config={{
                [metric]: {
                  label: metric.charAt(0).toUpperCase() + metric.slice(1),
                  color: "hsl(var(--chart-1))",
                },
              }}
              className="h-full"
            >
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={Object.keys(platformIcons).map((p) => ({
                    platform: p.charAt(0).toUpperCase() + p.slice(1),
                    [metric]: getMetricValue(p, metric).value,
                  }))}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="platform" />
                  <YAxis />
                  <ChartTooltip content={<ChartTooltipContent />} />
                  <Legend />
                  <Bar dataKey={metric} fill="var(--color-followers)" />
                </BarChart>
              </ResponsiveContainer>
            </ChartContainer>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Content Performance</CardTitle>
            <CardDescription>Top performing content on {platform}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[1, 2, 3].map((i) => (
                <div key={i} className="flex items-center gap-4">
                  <div className="h-16 w-16 rounded-md bg-muted flex items-center justify-center">
                    {platformIcons[platform as keyof typeof platformIcons]}
                  </div>
                  <div className="flex-1">
                    <div className="font-medium">Top Post #{i}</div>
                    <div className="text-sm text-muted-foreground">
                      {Math.floor(Math.random() * 10000).toLocaleString()} likes •{" "}
                      {Math.floor(Math.random() * 1000).toLocaleString()} comments
                    </div>
                  </div>
                  <Badge>+{Math.floor(Math.random() * 20) + 5}%</Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
