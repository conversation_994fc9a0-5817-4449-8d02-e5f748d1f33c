"use client"

import { useRef, useEffect } from "react"
import Image from "next/image"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { ArrowRight, Instagram, Youtube, Facebook, Play, Star, Sparkles } from "lucide-react"
import { TikTok } from "@/components/tiktok"
import gsap from "gsap"
import { registerGSAP } from "@/lib/gsap"

export function HeroSection() {
  const heroRef = useRef<HTMLElement>(null)
  const titleRef = useRef<HTMLHeadingElement>(null)
  const descRef = useRef<HTMLParagraphElement>(null)
  const ctaRef = useRef<HTMLDivElement>(null)
  const statsRef = useRef<HTMLDivElement>(null)
  const imageRef = useRef<HTMLDivElement>(null)
  const socialRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    registerGSAP()

    // Create a timeline for entrance animation
    const tl = gsap.timeline({ defaults: { ease: "power3.out" } })

    tl.fromTo(titleRef.current, { opacity: 0, y: 50 }, { opacity: 1, y: 0, duration: 0.8 })
      .fromTo(descRef.current, { opacity: 0, y: 30 }, { opacity: 1, y: 0, duration: 0.6 }, "-=0.4")
      .fromTo(ctaRef.current, { opacity: 0, y: 20 }, { opacity: 1, y: 0, duration: 0.6 }, "-=0.3")
      .fromTo(socialRef.current, { opacity: 0 }, { opacity: 1, duration: 0.6 }, "-=0.3")
      .fromTo(imageRef.current, { opacity: 0, scale: 0.9, x: 30 }, { opacity: 1, scale: 1, x: 0, duration: 1 }, "-=1")

    // Animate stats separately with null check
    const statItems = statsRef.current?.querySelectorAll(".stat-item")
    if (statItems) {
      tl.fromTo(
        statItems,
        { opacity: 0, y: 20 },
        { opacity: 1, y: 0, stagger: 0.1, duration: 0.6 },
        "-=0.5",
      )
    }

    // Add a subtle parallax effect on scroll
    gsap.to(imageRef.current, {
      y: 50,
      scrollTrigger: {
        trigger: heroRef.current,
        start: "top top",
        end: "bottom top",
        scrub: true,
      },
    })

    return () => {
      tl.kill()
    }
  }, [])

  return (
    <section ref={heroRef} className="relative overflow-hidden py-20 md:py-32 min-h-screen flex items-center">
      {/* Enhanced Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-brand-purple-light/20 via-background to-brand-blue-light/20 animate-gradient-x -z-10" />

      {/* Animated mesh gradient */}
      <div className="absolute inset-0 opacity-30 -z-10">
        <div className="absolute top-0 -left-4 w-72 h-72 bg-brand-purple rounded-full mix-blend-multiply filter blur-xl animate-blob" />
        <div className="absolute top-0 -right-4 w-72 h-72 bg-brand-accent-yellow rounded-full mix-blend-multiply filter blur-xl animate-blob animation-delay-2000" />
        <div className="absolute -bottom-8 left-20 w-72 h-72 bg-brand-accent-pink rounded-full mix-blend-multiply filter blur-xl animate-blob animation-delay-4000" />
      </div>

      {/* Floating particles */}
      <div className="absolute inset-0 -z-10">
        <div className="absolute top-1/4 left-1/4 w-2 h-2 bg-brand-accent-cyan rounded-full animate-ping" />
        <div className="absolute top-3/4 right-1/4 w-1 h-1 bg-brand-accent-pink rounded-full animate-pulse" />
        <div className="absolute top-1/2 left-3/4 w-1.5 h-1.5 bg-brand-accent-yellow rounded-full animate-bounce" />
      </div>

      <div className="container px-4 md:px-6 relative z-10">
        <div className="grid gap-12 lg:grid-cols-2 lg:gap-16 items-center">
          <div className="flex flex-col gap-8">
            {/* Enhanced badge */}
            <div className="inline-flex items-center rounded-full border border-brand-purple/20 bg-brand-purple/5 px-4 py-2 text-sm backdrop-blur-sm">
              <Sparkles className="w-4 h-4 mr-2 text-brand-accent-yellow" />
              <span className="font-medium bg-gradient-to-r from-brand-purple to-brand-blue bg-clip-text text-transparent">
                Launching Soon
              </span>
              <div className="ml-2 h-2 w-2 rounded-full bg-brand-accent-pink animate-pulse" />
            </div>

            {/* Enhanced title */}
            <div className="space-y-4">
              <h1 ref={titleRef} className="text-5xl font-bold tracking-tighter sm:text-6xl md:text-7xl leading-tight">
                Connect{" "}
                <span className="relative">
                  <span className="gradient-text">Influencers</span>
                  <div className="absolute -top-2 -right-2">
                    <Star className="w-6 h-6 text-brand-accent-yellow fill-current animate-pulse" />
                  </div>
                </span>{" "}
                with{" "}
                <span className="gradient-text">Brands</span>
              </h1>
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <div className="flex -space-x-2">
                  <div className="w-8 h-8 rounded-full bg-brand-purple/20 border-2 border-background" />
                  <div className="w-8 h-8 rounded-full bg-brand-blue/20 border-2 border-background" />
                  <div className="w-8 h-8 rounded-full bg-brand-accent-pink/20 border-2 border-background" />
                </div>
                <span>Trusted by 10,000+ creators</span>
              </div>
            </div>

            <p ref={descRef} className="text-muted-foreground text-xl md:text-2xl max-w-[600px] leading-relaxed">
              The platform that connects micro and macro influencers with brands looking for advertisers, affiliates,
              and collaborators. <span className="text-brand-purple font-semibold">Start earning today.</span>
            </p>

            {/* Enhanced CTA buttons */}
            <div ref={ctaRef} className="flex flex-col sm:flex-row gap-4 mt-4">
              <Link href="/signup">
                <Button size="lg" className="glow-effect text-lg px-8 py-6 rounded-full">
                  Get Started Free <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
              <Link href="#demo">
                <Button size="lg" variant="outline" className="gradient-border text-lg px-8 py-6 rounded-full group">
                  <Play className="mr-2 h-5 w-5 group-hover:scale-110 transition-transform" />
                  Watch Demo
                </Button>
              </Link>
            </div>

            {/* Enhanced social proof */}
            <div ref={socialRef} className="flex flex-col sm:flex-row sm:items-center gap-4 mt-6">
              <div className="flex items-center gap-2">
                <p className="text-sm text-muted-foreground">Connect across:</p>
                <div className="flex gap-3">
                  <div className="p-2 rounded-lg bg-gradient-to-br from-pink-500 to-rose-500 text-white hover:scale-110 transition-transform cursor-pointer">
                    <Instagram className="h-4 w-4" />
                  </div>
                  <div className="p-2 rounded-lg bg-gradient-to-br from-red-500 to-red-600 text-white hover:scale-110 transition-transform cursor-pointer">
                    <Youtube className="h-4 w-4" />
                  </div>
                  <div className="p-2 rounded-lg bg-gradient-to-br from-gray-800 to-gray-900 text-white hover:scale-110 transition-transform cursor-pointer">
                    <TikTok className="h-4 w-4" />
                  </div>
                  <div className="p-2 rounded-lg bg-gradient-to-br from-blue-500 to-blue-600 text-white hover:scale-110 transition-transform cursor-pointer">
                    <Facebook className="h-4 w-4" />
                  </div>
                </div>
              </div>
              <div className="flex items-center gap-1">
                <div className="flex">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="w-4 h-4 text-brand-accent-yellow fill-current" />
                  ))}
                </div>
                <span className="text-sm text-muted-foreground ml-1">4.9/5 from 2,000+ reviews</span>
              </div>
            </div>
          </div>

          {/* Enhanced image section */}
          <div ref={imageRef} className="relative">
            {/* Main dashboard mockup */}
            <div className="relative z-10 rounded-2xl overflow-hidden shadow-2xl animate-float bg-gradient-to-br from-white to-gray-50 dark:from-gray-900 dark:to-gray-800 border border-gray-200 dark:border-gray-700">
              <div className="p-6">
                {/* Mock browser bar */}
                <div className="flex items-center gap-2 mb-4">
                  <div className="w-3 h-3 rounded-full bg-red-500" />
                  <div className="w-3 h-3 rounded-full bg-yellow-500" />
                  <div className="w-3 h-3 rounded-full bg-green-500" />
                </div>
                {/* Mock dashboard content */}
                <div className="space-y-4">
                  <div className="h-8 bg-gradient-to-r from-brand-purple to-brand-blue rounded-lg" />
                  <div className="grid grid-cols-3 gap-4">
                    <div className="h-20 bg-brand-accent-pink/20 rounded-lg" />
                    <div className="h-20 bg-brand-accent-cyan/20 rounded-lg" />
                    <div className="h-20 bg-brand-accent-yellow/20 rounded-lg" />
                  </div>
                  <div className="h-32 bg-gradient-to-br from-brand-purple/10 to-brand-blue/10 rounded-lg" />
                </div>
              </div>
            </div>

            {/* Floating elements */}
            <div className="absolute -top-4 -right-4 w-16 h-16 bg-brand-accent-pink rounded-full flex items-center justify-center shadow-lg animate-bounce">
              <span className="text-white font-bold text-sm">$$$</span>
            </div>
            <div className="absolute -bottom-4 -left-4 w-12 h-12 bg-brand-accent-cyan rounded-full flex items-center justify-center shadow-lg animate-pulse">
              <Star className="w-6 h-6 text-white fill-current" />
            </div>

            {/* Background glow */}
            <div className="absolute -inset-4 bg-gradient-to-r from-brand-purple/20 to-brand-blue/20 rounded-3xl blur-2xl -z-10" />
          </div>
        </div>

        {/* Enhanced Stats */}
        <div ref={statsRef} className="grid grid-cols-2 md:grid-cols-4 gap-8 mt-24 pt-12 border-t border-gray-200 dark:border-gray-800">
          <div className="flex flex-col items-center text-center stat-item group">
            <div className="relative">
              <p className="text-4xl font-bold bg-gradient-to-r from-brand-purple to-brand-blue bg-clip-text text-transparent">
                10K+
              </p>
              <div className="absolute -top-1 -right-1 w-2 h-2 bg-brand-accent-pink rounded-full animate-ping" />
            </div>
            <p className="text-muted-foreground mt-1 group-hover:text-foreground transition-colors">Active Influencers</p>
          </div>
          <div className="flex flex-col items-center text-center stat-item group">
            <p className="text-4xl font-bold bg-gradient-to-r from-brand-blue to-brand-accent-cyan bg-clip-text text-transparent">
              5K+
            </p>
            <p className="text-muted-foreground mt-1 group-hover:text-foreground transition-colors">Partner Brands</p>
          </div>
          <div className="flex flex-col items-center text-center stat-item group">
            <p className="text-4xl font-bold bg-gradient-to-r from-brand-accent-pink to-brand-accent-yellow bg-clip-text text-transparent">
              50M+
            </p>
            <p className="text-muted-foreground mt-1 group-hover:text-foreground transition-colors">Audience Reach</p>
          </div>
          <div className="flex flex-col items-center text-center stat-item group">
            <p className="text-4xl font-bold bg-gradient-to-r from-brand-accent-yellow to-brand-purple bg-clip-text text-transparent">
              $2M+
            </p>
            <p className="text-muted-foreground mt-1 group-hover:text-foreground transition-colors">Creator Earnings</p>
          </div>
        </div>
      </div>
    </section>
  )
}
