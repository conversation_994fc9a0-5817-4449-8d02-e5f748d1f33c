"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { But<PERSON> } from "@/components/ui/button"
import {
  BarChart3,
  Home,
  Users,
  DollarSign,
  Globe,
  Settings,
  LogOut,
  Menu,
  X,
  LineChart,
  Pie<PERSON>hart,
  TrendingUp,
} from "lucide-react"
import { cn } from "@/lib/utils"
import { useState } from "react"

export function AnalyticsSidebar() {
  const pathname = usePathname()
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)

  const routes = [
    {
      label: "Dashboard",
      icon: Home,
      href: "/analytics",
      active: pathname === "/analytics",
    },
    {
      label: "Social Media",
      icon: Globe,
      href: "/analytics/social",
      active: pathname === "/analytics/social",
    },
    {
      label: "Campaigns",
      icon: BarChart3,
      href: "/analytics/campaigns",
      active: pathname === "/analytics/campaigns",
    },
    {
      label: "Audience",
      icon: Users,
      href: "/analytics/audience",
      active: pathname === "/analytics/audience",
    },
    {
      label: "Content Performance",
      icon: LineChart,
      href: "/analytics/content",
      active: pathname === "/analytics/content",
    },
    {
      label: "Demographics",
      icon: PieChart,
      href: "/analytics/demographics",
      active: pathname === "/analytics/demographics",
    },
    {
      label: "ROI Analysis",
      icon: DollarSign,
      href: "/analytics/roi",
      active: pathname === "/analytics/roi",
    },
    {
      label: "Growth Trends",
      icon: TrendingUp,
      href: "/analytics/growth",
      active: pathname === "/analytics/growth",
    },
    {
      label: "Settings",
      icon: Settings,
      href: "/analytics/settings",
      active: pathname === "/analytics/settings",
    },
  ]

  return (
    <>
      <Button
        variant="outline"
        size="icon"
        className="fixed top-4 left-4 z-50 md:hidden"
        onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
      >
        {isMobileMenuOpen ? <X className="h-4 w-4" /> : <Menu className="h-4 w-4" />}
      </Button>

      <div
        className={cn(
          "fixed inset-y-0 left-0 z-40 w-1/4 bg-background border-r transform transition-transform duration-200 ease-in-out md:translate-x-0 overflow-y-auto",
          isMobileMenuOpen ? "translate-x-0" : "-translate-x-full",
        )}
      >
        <div className="flex flex-col h-full">
          <div className="p-6">
            <Link href="/" className="flex items-center gap-2">
              <span className="text-2xl font-bold gradient-text">Influenzy</span>
            </Link>
            <div className="mt-2 text-sm text-muted-foreground">Analytics Dashboard</div>
          </div>

          <div className="flex-1 px-3 py-2">
            <div className="flex flex-col gap-1">
              {routes.map((route) => (
                <Link
                  key={route.href}
                  href={route.href}
                  className={cn(
                    "flex items-center gap-3 rounded-lg px-3 py-2 text-sm transition-colors",
                    route.active
                      ? "bg-primary text-primary-foreground"
                      : "hover:bg-muted text-muted-foreground hover:text-foreground",
                  )}
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  <route.icon className="h-4 w-4" />
                  {route.label}
                </Link>
              ))}
            </div>
          </div>

          <div className="mt-auto p-4 border-t">
            <div className="flex items-center gap-3 mb-4">
              <Avatar>
                <AvatarImage src="/placeholder.svg" />
                <AvatarFallback>JD</AvatarFallback>
              </Avatar>
              <div>
                <p className="text-sm font-medium">Jane Doe</p>
                <p className="text-xs text-muted-foreground">@janedoe</p>
              </div>
            </div>
            <Button variant="outline" className="w-full justify-start" size="sm">
              <LogOut className="mr-2 h-4 w-4" />
              Log out
            </Button>
          </div>
        </div>
      </div>
    </>
  )
}
