"use client"

import { useState } from "react"
import { BrandLayout } from "@/components/brand-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table"
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Instagram, Youtube, Search, Filter, MapPin, ExternalLink, Heart, MessageSquare } from "lucide-react"

export function CreatorDiscovery() {
  const [categoryFilter, setCategoryFilter] = useState("all")
  const [platformFilter, setPlatformFilter] = useState("all")
  const [locationFilter, setLocationFilter] = useState("all")
  const [searchQuery, setSearchQuery] = useState("")

  // Mock data for creators
  const creators = [
    {
      id: 1,
      name: "Alex Johnson",
      username: "@alexjohnson",
      avatar: "/placeholder.svg",
      location: "New York, USA",
      category: "Fashion",
      bio: "Fashion and lifestyle content creator. Sharing my daily outfits and style tips.",
      platforms: {
        instagram: { followers: 1300000, handle: "@alexjohnson" },
        youtube: { followers: 5400000, handle: "@alexjohnson" },
        tiktok: { followers: 786300, handle: "@alexjohnson" }
      },
      engagementRate: "3.2%",
      averageLikes: 42000,
      averageComments: 1200
    },
    {
      id: 2,
      name: "Sarah Miller",
      username: "@sarahmiller",
      avatar: "/placeholder.svg",
      location: "Los Angeles, USA",
      category: "Beauty",
      bio: "Beauty enthusiast and makeup artist. Creating tutorials and product reviews.",
      platforms: {
        instagram: { followers: 850000, handle: "@sarahmiller" },
        youtube: { followers: 2100000, handle: "@sarahmiller" },
        tiktok: { followers: 1500000, handle: "@sarahmiller" }
      },
      engagementRate: "4.5%",
      averageLikes: 38000,
      averageComments: 2200
    },
    {
      id: 3,
      name: "Mike Chen",
      username: "@mikechen",
      avatar: "/placeholder.svg",
      location: "Chicago, USA",
      category: "Fitness",
      bio: "Fitness coach and nutrition expert. Sharing workout routines and healthy recipes.",
      platforms: {
        instagram: { followers: 620000, handle: "@mikechen" },
        youtube: { followers: 1800000, handle: "@mikechen" },
        tiktok: { followers: 950000, handle: "@mikechen" }
      },
      engagementRate: "5.1%",
      averageLikes: 31500,
      averageComments: 1850
    },
    {
      id: 4,
      name: "Emily Davis",
      username: "@emilydavis",
      avatar: "/placeholder.svg",
      location: "London, UK",
      category: "Travel",
      bio: "Travel blogger and photographer. Exploring the world and sharing my adventures.",
      platforms: {
        instagram: { followers: 980000, handle: "@emilydavis" },
        youtube: { followers: 1200000, handle: "@emilydavis" },
        tiktok: { followers: 750000, handle: "@emilydavis" }
      },
      engagementRate: "3.8%",
      averageLikes: 37200,
      averageComments: 1600
    },
    {
      id: 5,
      name: "David Kim",
      username: "@davidkim",
      avatar: "/placeholder.svg",
      location: "Seoul, South Korea",
      category: "Technology",
      bio: "Tech reviewer and gadget enthusiast. Covering the latest in consumer electronics.",
      platforms: {
        instagram: { followers: 450000, handle: "@davidkim" },
        youtube: { followers: 3200000, handle: "@davidkim" },
        tiktok: { followers: 680000, handle: "@davidkim" }
      },
      engagementRate: "4.2%",
      averageLikes: 18900,
      averageComments: 3200
    },
    {
      id: 6,
      name: "Sophia Martinez",
      username: "@sophiamartinez",
      avatar: "/placeholder.svg",
      location: "Miami, USA",
      category: "Food",
      bio: "Food blogger and recipe developer. Creating delicious and easy-to-follow recipes.",
      platforms: {
        instagram: { followers: 720000, handle: "@sophiamartinez" },
        youtube: { followers: 1500000, handle: "@sophiamartinez" },
        tiktok: { followers: 2100000, handle: "@sophiamartinez" }
      },
      engagementRate: "5.7%",
      averageLikes: 41000,
      averageComments: 2800
    }
  ]

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + "M"
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + "K"
    }
    return num.toString()
  }

  const getPlatformIcon = (platform: string) => {
    switch (platform) {
      case "instagram":
        return <Instagram className="h-4 w-4" />
      case "youtube":
        return <Youtube className="h-4 w-4" />
      case "tiktok":
        return (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="h-4 w-4"
          >
            <path d="M9 12a4 4 0 1 0 0 8 4 4 0 0 0 0-8z" />
            <path d="M15 8a4 4 0 1 0 0-8 4 4 0 0 0 0 8z" />
            <path d="M15 8v8a4 4 0 0 1-4 4" />
            <line x1="15" y1="4" x2="15" y2="12" />
          </svg>
        )
      default:
        return null
    }
  }

  const filteredCreators = creators.filter(creator => {
    // Filter by category
    if (categoryFilter !== "all" && creator.category !== categoryFilter) return false
    
    // Filter by platform
    if (platformFilter !== "all" && !creator.platforms[platformFilter as keyof typeof creator.platforms]) return false
    
    // Filter by location
    if (locationFilter !== "all" && !creator.location.includes(locationFilter)) return false
    
    // Filter by search query
    if (searchQuery && !creator.name.toLowerCase().includes(searchQuery.toLowerCase()) && 
        !creator.username.toLowerCase().includes(searchQuery.toLowerCase()) &&
        !creator.bio.toLowerCase().includes(searchQuery.toLowerCase())) return false
    
    return true
  })

  const uniqueCategories = Array.from(new Set(creators.map(creator => creator.category)))
  const uniqueLocations = Array.from(new Set(creators.map(creator => creator.location.split(", ")[1]))) // Get countries

  return (
    <BrandLayout>
      <div className="flex flex-col gap-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Discover Creators</h1>
            <p className="text-muted-foreground">Find the perfect influencers for your campaigns</p>
          </div>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Search Creators</CardTitle>
            <CardDescription>Find creators by name, category, platform, or location</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col gap-4">
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input 
                  placeholder="Search by name, username, or bio..." 
                  className="pl-8" 
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
              
              <div className="flex flex-wrap gap-4 items-center">
                <div className="flex items-center gap-2">
                  <Filter className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium">Filter by:</span>
                </div>
                <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                  <SelectTrigger className="w-[150px]">
                    <SelectValue placeholder="Category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    {uniqueCategories.map(category => (
                      <SelectItem key={category} value={category}>{category}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Select value={platformFilter} onValueChange={setPlatformFilter}>
                  <SelectTrigger className="w-[150px]">
                    <SelectValue placeholder="Platform" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Platforms</SelectItem>
                    <SelectItem value="instagram">Instagram</SelectItem>
                    <SelectItem value="youtube">YouTube</SelectItem>
                    <SelectItem value="tiktok">TikTok</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={locationFilter} onValueChange={setLocationFilter}>
                  <SelectTrigger className="w-[150px]">
                    <SelectValue placeholder="Location" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Locations</SelectItem>
                    {uniqueLocations.map(location => (
                      <SelectItem key={location} value={location}>{location}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle>Creator Results</CardTitle>
            <CardDescription>
              {filteredCreators.length} creators found matching your criteria
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Creator</TableHead>
                    <TableHead>Category</TableHead>
                    <TableHead>Location</TableHead>
                    <TableHead>Followers</TableHead>
                    <TableHead>Engagement</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredCreators.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-6 text-muted-foreground">
                        No creators found matching your criteria
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredCreators.map((creator) => (
                      <TableRow key={creator.id}>
                        <TableCell>
                          <div className="flex items-center gap-3">
                            <Avatar className="h-10 w-10">
                              <AvatarImage src={creator.avatar} alt={creator.name} />
                              <AvatarFallback>{creator.name.substring(0, 2)}</AvatarFallback>
                            </Avatar>
                            <div>
                              <div className="font-medium">{creator.name}</div>
                              <div className="text-sm text-muted-foreground">{creator.username}</div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">{creator.category}</Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-1">
                            <MapPin className="h-3 w-3 text-muted-foreground" />
                            <span>{creator.location}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            {Object.entries(creator.platforms).map(([platform, data]) => (
                              <div key={platform} className="flex items-center gap-1.5">
                                {getPlatformIcon(platform)}
                                <span className="text-sm font-medium">{formatNumber(data.followers)}</span>
                                {platform === "youtube" && (
                                  <Badge variant="outline" className="text-xs py-0 h-4">+1</Badge>
                                )}
                              </div>
                            ))}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            <div className="text-sm">Rate: {creator.engagementRate}</div>
                            <div className="flex items-center gap-3 text-xs text-muted-foreground">
                              <div className="flex items-center gap-1">
                                <Heart className="h-3 w-3" />
                                {formatNumber(creator.averageLikes)}
                              </div>
                              <div className="flex items-center gap-1">
                                <MessageSquare className="h-3 w-3" />
                                {formatNumber(creator.averageComments)}
                              </div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Button variant="outline" size="sm">View Profile</Button>
                            <Button size="sm">Contact</Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      </div>
    </BrandLayout>
  )
}
