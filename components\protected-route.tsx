"use client"

import type React from "react"

import { useEffect } from "react"
import { useRouter } from "next/navigation"
import { useAuth, type UserRole } from "@/contexts/auth-context"

interface ProtectedRouteProps {
  children: React.ReactNode
  allowedRoles?: UserRole[]
}

export function ProtectedRoute({ children, allowedRoles }: ProtectedRouteProps) {
  const { user, isLoading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!isLoading && !user) {
      router.push("/login")
    } else if (!isLoading && user && allowedRoles && !allowedRoles.includes(user.role)) {
      // Redirect based on role if they're accessing a page they shouldn't
      if (user.role === "creator") {
        router.push("/influencer/dashboard")
      } else if (user.role === "brand") {
        router.push("/brand/dashboard")
      } else {
        router.push("/")
      }
    }
  }, [user, isLoading, router, allowedRoles])

  // Show loading or nothing while checking authentication
  if (isLoading || !user) {
    return null
  }

  // If roles are specified and user doesn't have permission, don't render children
  if (allowedRoles && !allowedRoles.includes(user.role)) {
    return null
  }

  return <>{children}</>
}
