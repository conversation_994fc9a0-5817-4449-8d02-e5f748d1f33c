"use client"

import { useRef, useEffect } from "react"
import Image from "next/image"
import { Badge } from "@/components/ui/badge"
import { ArrowRight, UserPlus, Search, Rocket, BarChart3 } from "lucide-react"
import gsap from "gsap"
import { registerGSAP, cleanupScrollTriggers } from "@/lib/gsap"

export function HowItWorksSection() {
  const sectionRef = useRef<HTMLElement>(null)
  const headingRef = useRef<HTMLDivElement>(null)
  const stepsRef = useRef<HTMLDivElement>(null)

  const steps = [
    {
      number: "01",
      badge: "For Influencers",
      title: "Create Your Profile",
      description: "Sign up and connect your social media accounts to automatically import your stats and content.",
      icon: <UserPlus className="w-8 h-8" />,
      gradient: "from-brand-purple to-brand-purple-dark",
      bgColor: "bg-brand-purple/10",
    },
    {
      number: "02",
      badge: "For Brands",
      title: "Discover Creators",
      description:
        "Browse through thousands of influencers filtered by niche, audience size, engagement rate, and more.",
      icon: <Search className="w-8 h-8" />,
      gradient: "from-brand-blue to-brand-blue-dark",
      bgColor: "bg-brand-blue/10",
    },
    {
      number: "03",
      badge: "Collaboration",
      title: "Launch Campaigns",
      description: "Create campaign briefs, send collaboration requests, and manage the entire process in one place.",
      icon: <Rocket className="w-8 h-8" />,
      gradient: "from-brand-accent-pink to-rose-500",
      bgColor: "bg-brand-accent-pink/10",
    },
    {
      number: "04",
      badge: "Analytics",
      title: "Track Results",
      description: "Monitor campaign performance with real-time analytics and comprehensive reports.",
      icon: <BarChart3 className="w-8 h-8" />,
      gradient: "from-brand-accent-cyan to-teal-500",
      bgColor: "bg-brand-accent-cyan/10",
    },
  ]

  useEffect(() => {
    registerGSAP()

    // Animate heading
    gsap.fromTo(
      headingRef.current,
      { opacity: 0, y: 50 },
      {
        opacity: 1,
        y: 0,
        duration: 0.8,
        scrollTrigger: {
          trigger: headingRef.current,
          start: "top 80%",
          end: "bottom 20%",
          toggleActions: "play none none reverse",
        },
      },
    )

    // Animate steps
    const stepElements = stepsRef.current?.querySelectorAll(".step-item")
    if (stepElements) {
      stepElements.forEach((step, index) => {
        gsap.fromTo(
          step,
          { opacity: 0, y: 100, scale: 0.9 },
          {
            opacity: 1,
            y: 0,
            scale: 1,
            duration: 0.8,
            delay: index * 0.2,
            scrollTrigger: {
              trigger: step,
              start: "top 85%",
              end: "bottom 15%",
              toggleActions: "play none none reverse",
            },
          },
        )
      })
    }

    return () => {
      cleanupScrollTriggers()
    }
  }, [])

  return (
    <section id="how-it-works" ref={sectionRef} className="py-24 bg-gradient-to-b from-background to-muted/30">
      <div className="container px-4 md:px-6">
        <div ref={headingRef} className="flex flex-col items-center text-center mb-16">
          <div className="inline-flex items-center rounded-full border border-brand-purple/20 bg-brand-purple/5 px-4 py-2 text-sm backdrop-blur-sm mb-4">
            <span className="font-medium bg-gradient-to-r from-brand-purple to-brand-blue bg-clip-text text-transparent">
              Simple Process
            </span>
          </div>
          <h2 className="text-4xl font-bold tracking-tighter sm:text-5xl md:text-6xl mb-4">
            How <span className="gradient-text-animated">Influenzy</span> Works
          </h2>
          <p className="text-muted-foreground text-xl max-w-[800px] leading-relaxed">
            Our platform makes it easy for brands and influencers to connect, collaborate, and create amazing content
            together in just four simple steps.
          </p>
        </div>

        <div ref={stepsRef} className="relative">
          {/* Connection line */}
          <div className="absolute left-1/2 top-0 bottom-0 w-px bg-gradient-to-b from-brand-purple via-brand-blue to-brand-accent-cyan hidden lg:block" />
          
          <div className="space-y-24">
            {steps.map((step, index) => (
              <div
                key={index}
                className={`step-item relative grid gap-12 items-center ${
                  index % 2 === 0 ? "lg:grid-cols-[1fr_1fr]" : "lg:grid-cols-[1fr_1fr]"
                }`}
              >
                {/* Step number indicator */}
                <div className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 w-16 h-16 rounded-full bg-background border-4 border-brand-purple/20 flex items-center justify-center z-10 hidden lg:flex">
                  <span className="text-2xl font-bold bg-gradient-to-r from-brand-purple to-brand-blue bg-clip-text text-transparent">
                    {step.number}
                  </span>
                </div>

                {/* Content */}
                <div className={`${index % 2 !== 0 && "lg:order-2"} space-y-6`}>
                  <div className="flex items-center gap-4 lg:hidden">
                    <div className={`p-3 rounded-xl bg-gradient-to-br ${step.gradient} text-white`}>
                      {step.icon}
                    </div>
                    <Badge variant="outline" className={`${step.bgColor} border-current`}>
                      {step.badge}
                    </Badge>
                  </div>
                  
                  <div className="hidden lg:block">
                    <Badge variant="outline" className={`${step.bgColor} border-current mb-4`}>
                      {step.badge}
                    </Badge>
                  </div>

                  <h3 className="text-3xl font-bold leading-tight">{step.title}</h3>
                  <p className="text-muted-foreground text-lg leading-relaxed max-w-md">
                    {step.description}
                  </p>
                  
                  {index < steps.length - 1 && (
                    <div className="flex items-center gap-2 text-sm text-brand-purple font-medium">
                      <span>Next step</span>
                      <ArrowRight className="w-4 h-4" />
                    </div>
                  )}
                </div>

                {/* Visual */}
                <div className={`${index % 2 !== 0 && "lg:order-1"} relative group`}>
                  <div className="relative rounded-2xl overflow-hidden shadow-2xl bg-gradient-to-br from-white to-gray-50 dark:from-gray-900 dark:to-gray-800 border border-gray-200 dark:border-gray-700 group-hover:shadow-3xl transition-shadow duration-500">
                    {/* Mock interface */}
                    <div className="p-8">
                      <div className="flex items-center gap-2 mb-6">
                        <div className="w-3 h-3 rounded-full bg-red-500" />
                        <div className="w-3 h-3 rounded-full bg-yellow-500" />
                        <div className="w-3 h-3 rounded-full bg-green-500" />
                      </div>
                      
                      <div className="space-y-4">
                        <div className={`h-12 bg-gradient-to-r ${step.gradient} rounded-lg flex items-center justify-center text-white`}>
                          {step.icon}
                        </div>
                        <div className="space-y-3">
                          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4" />
                          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2" />
                          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-2/3" />
                        </div>
                        <div className="grid grid-cols-2 gap-4 mt-6">
                          <div className={`h-16 ${step.bgColor} rounded-lg`} />
                          <div className={`h-16 ${step.bgColor} rounded-lg`} />
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  {/* Floating elements */}
                  <div className={`absolute -top-4 -right-4 w-8 h-8 bg-gradient-to-br ${step.gradient} rounded-full flex items-center justify-center shadow-lg animate-float-slow`}>
                    <div className="w-2 h-2 bg-white rounded-full" />
                  </div>
                  
                  {/* Glow effect */}
                  <div className={`absolute -inset-4 bg-gradient-to-r ${step.gradient} rounded-3xl blur-2xl opacity-20 group-hover:opacity-30 transition-opacity duration-500 -z-10`} />
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}
