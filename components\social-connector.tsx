"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Icons } from "@/components/icons"
import { useAuth, type SocialPlatform } from "@/contexts/auth-context"

interface SocialConnectorProps {
  platform: SocialPlatform
}

export function SocialConnector({ platform }: SocialConnectorProps) {
  const { user, connectSocialPlatform, disconnectSocialPlatform } = useAuth()
  const [isConnecting, setIsConnecting] = useState(false)

  const isConnected = user?.connectedPlatforms?.includes(platform)

  const handleConnect = async () => {
    setIsConnecting(true)
    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1500))
      connectSocialPlatform(platform)
    } finally {
      setIsConnecting(false)
    }
  }

  const handleDisconnect = () => {
    disconnectSocialPlatform(platform)
  }

  const getPlatformIcon = () => {
    switch (platform) {
      case "instagram":
        return <Icons.instagram className="h-5 w-5" />
      case "youtube":
        return <Icons.youtube className="h-5 w-5" />
      case "facebook":
        return <Icons.facebook className="h-5 w-5" />
      case "tiktok":
        return <Icons.tiktok className="h-5 w-5" />
      default:
        return null
    }
  }

  const getPlatformName = () => {
    return platform.charAt(0).toUpperCase() + platform.slice(1)
  }

  return (
    <div className="flex items-center justify-between p-4 border rounded-lg">
      <div className="flex items-center gap-3">
        {getPlatformIcon()}
        <div>
          <h3 className="font-medium">{getPlatformName()}</h3>
          <p className="text-sm text-muted-foreground">
            {isConnected ? "Connected" : `Connect your ${getPlatformName()} account`}
          </p>
        </div>
      </div>
      {isConnected ? (
        <Button variant="outline" size="sm" onClick={handleDisconnect}>
          Disconnect
        </Button>
      ) : (
        <Button size="sm" onClick={handleConnect} disabled={isConnecting}>
          {isConnecting ? (
            <>
              <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
              Connecting...
            </>
          ) : (
            "Connect"
          )}
        </Button>
      )}
    </div>
  )
}
