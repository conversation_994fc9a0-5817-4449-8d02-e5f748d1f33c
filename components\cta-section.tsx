"use client"

import { useRef, useEffect } from "react"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>R<PERSON>, <PERSON>rkles, Star, Zap } from "lucide-react"
import gsap from "gsap"
import { registerGSAP, cleanupScrollTriggers } from "@/lib/gsap"

export function CTASection() {
  const sectionRef = useRef<HTMLElement>(null)
  const ctaRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    registerGSAP()

    gsap.fromTo(
      ctaRef.current,
      { opacity: 0, y: 50, scale: 0.95 },
      {
        opacity: 1,
        y: 0,
        scale: 1,
        duration: 1,
        scrollTrigger: {
          trigger: ctaRef.current,
          start: "top 80%",
          end: "bottom 20%",
          toggleActions: "play none none reverse",
        },
      },
    )

    // Animate floating elements
    const floatingElements = ctaRef.current?.querySelectorAll(".floating-element")
    if (floatingElements) {
      floatingElements.forEach((element, index) => {
        gsap.to(element, {
          y: -20,
          duration: 2 + index * 0.5,
          repeat: -1,
          yoyo: true,
          ease: "power2.inOut",
          delay: index * 0.3,
        })
      })
    }

    return () => {
      cleanupScrollTriggers()
    }
  }, [])

  return (
    <section ref={sectionRef} className="py-24 relative overflow-hidden">
      {/* Background elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-brand-purple/5 via-background to-brand-blue/5" />
      <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-brand-purple/10 rounded-full blur-3xl animate-blob" />
      <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-brand-blue/10 rounded-full blur-3xl animate-blob animation-delay-2000" />

      <div className="container px-4 md:px-6 relative z-10">
        <div 
          ref={ctaRef}
          className="relative overflow-hidden rounded-3xl bg-gradient-to-br from-brand-purple via-brand-blue to-brand-accent-cyan p-12 md:p-20 shadow-2xl"
        >
          {/* Animated background pattern */}
          <div className="absolute inset-0 opacity-10">
            <div className="absolute top-0 left-0 w-full h-full bg-[radial-gradient(circle_at_50%_50%,rgba(255,255,255,0.1)_1px,transparent_1px)] bg-[length:50px_50px] animate-pulse-slow" />
          </div>

          {/* Floating decorative elements */}
          <div className="absolute top-8 right-8 floating-element">
            <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
              <Sparkles className="w-6 h-6 text-white" />
            </div>
          </div>
          <div className="absolute top-1/2 right-16 floating-element">
            <div className="w-8 h-8 bg-brand-accent-yellow/30 rounded-full flex items-center justify-center">
              <Star className="w-4 h-4 text-white fill-current" />
            </div>
          </div>
          <div className="absolute bottom-8 left-8 floating-element">
            <div className="w-10 h-10 bg-brand-accent-pink/30 rounded-full flex items-center justify-center">
              <Zap className="w-5 h-5 text-white" />
            </div>
          </div>

          <div className="relative z-10 max-w-4xl">
            <div className="text-center mb-8">
              <div className="inline-flex items-center rounded-full bg-white/20 backdrop-blur-sm px-4 py-2 text-sm text-white mb-6">
                <Sparkles className="w-4 h-4 mr-2" />
                <span className="font-medium">Limited Time Offer</span>
              </div>
              
              <h2 className="text-4xl font-bold tracking-tighter sm:text-5xl md:text-6xl text-white mb-6 leading-tight">
                Ready to Transform Your{" "}
                <span className="relative">
                  Influencer Marketing?
                  <div className="absolute -bottom-2 left-0 right-0 h-1 bg-brand-accent-yellow rounded-full" />
                </span>
              </h2>
              
              <p className="text-xl text-white/90 max-w-3xl mx-auto leading-relaxed mb-8">
                Join thousands of influencers and brands already using Influenzy to create successful partnerships and
                grow their business. <span className="font-semibold">Start your free trial today!</span>
              </p>

              {/* Stats */}
              <div className="grid grid-cols-3 gap-8 mb-12 max-w-2xl mx-auto">
                <div className="text-center">
                  <div className="text-3xl font-bold text-white mb-1">10K+</div>
                  <div className="text-white/80 text-sm">Active Users</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-white mb-1">$2M+</div>
                  <div className="text-white/80 text-sm">Paid Out</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-white mb-1">98%</div>
                  <div className="text-white/80 text-sm">Satisfaction</div>
                </div>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
              <Link href="/signup">
                <Button 
                  size="lg" 
                  className="bg-white text-brand-purple hover:bg-white/90 text-lg px-8 py-6 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 magnetic"
                >
                  Start Free Trial <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
              <Link href="/demo">
                <Button 
                  size="lg" 
                  variant="outline" 
                  className="border-2 border-white text-white hover:bg-white/10 text-lg px-8 py-6 rounded-full backdrop-blur-sm transition-all duration-300"
                >
                  Book a Demo
                </Button>
              </Link>
            </div>

            <div className="text-center mt-8">
              <p className="text-white/70 text-sm">
                No credit card required • 14-day free trial • Cancel anytime
              </p>
            </div>
          </div>

          {/* Enhanced decorative elements */}
          <div className="absolute top-1/4 right-0 w-80 h-80 bg-white/5 rounded-full blur-3xl animate-pulse-slow" />
          <div className="absolute bottom-0 left-1/4 w-80 h-80 bg-white/5 rounded-full blur-3xl animate-pulse-slow animation-delay-2000" />
          <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-transparent via-white/5 to-transparent animate-shimmer" />
        </div>
      </div>
    </section>
  )
}
