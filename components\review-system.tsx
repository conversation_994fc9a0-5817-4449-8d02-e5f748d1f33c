"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Textarea } from "@/components/ui/textarea"
import { Star, ThumbsUp, MessageSquare } from "lucide-react"

export function ReviewSystem() {
  const [activeTab, setActiveTab] = useState("received")

  // Mock data for reviews
  const reviews = [
    {
      id: 1,
      type: "received",
      from: {
        name: "TechGadgets",
        avatar: "/placeholder.svg",
        role: "Brand",
      },
      rating: 5,
      comment:
        "<PERSON> was amazing to work with! Delivered the content on time and the quality exceeded our expectations. The engagement on the post was excellent.",
      date: "2023-06-15",
    },
    {
      id: 2,
      type: "given",
      to: {
        name: "Fashion<PERSON>rand",
        avatar: "/placeholder.svg",
        role: "Brand",
      },
      rating: 4,
      comment:
        "Great collaboration overall. Clear communication and fair compensation. Would work with them again in the future.",
      date: "2023-05-20",
    },
    {
      id: 3,
      type: "received",
      from: {
        name: "BeautyCompany",
        avatar: "/placeholder.svg",
        role: "Brand",
      },
      rating: 5,
      comment:
        "Professional and creative. Sarah understood our brand values and created content that perfectly aligned with our vision.",
      date: "2023-04-10",
    },
    {
      id: 4,
      type: "given",
      to: {
        name: "SportsGear",
        avatar: "/placeholder.svg",
        role: "Brand",
      },
      rating: 3,
      comment:
        "The collaboration was okay. There were some delays in communication and product delivery, but the final outcome was satisfactory.",
      date: "2023-03-15",
    },
  ]

  const filteredReviews = reviews.filter((review) => review.type === activeTab)

  return (
    <Card>
      <CardHeader>
        <CardTitle>Reviews & Ratings</CardTitle>
        <CardDescription>Manage your reputation and review collaborations</CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="received" onValueChange={setActiveTab}>
          <TabsList className="mb-4">
            <TabsTrigger value="received">Reviews Received</TabsTrigger>
            <TabsTrigger value="given">Reviews Given</TabsTrigger>
          </TabsList>

          <div className="space-y-4">
            {filteredReviews.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">No reviews found</div>
            ) : (
              filteredReviews.map((review) => (
                <Card key={review.id}>
                  <CardContent className="p-4">
                    <div className="flex items-start gap-4">
                      <Avatar>
                        <AvatarImage
                          src={review.type === "received" ? review.from.avatar : review.to.avatar}
                          alt={review.type === "received" ? review.from.name : review.to.name}
                        />
                        <AvatarFallback>
                          {(review.type === "received" ? review.from.name : review.to.name).substring(0, 2)}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1">
                        <div className="flex justify-between items-start">
                          <div>
                            <div className="font-medium">
                              {review.type === "received" ? review.from.name : review.to.name}
                            </div>
                            <div className="text-xs text-muted-foreground">
                              {review.type === "received" ? review.from.role : review.to.role}
                            </div>
                          </div>
                          <div className="text-xs text-muted-foreground">
                            {new Date(review.date).toLocaleDateString()}
                          </div>
                        </div>
                        <div className="flex items-center mt-1 mb-2">
                          {Array(5)
                            .fill(0)
                            .map((_, i) => (
                              <Star
                                key={i}
                                className={`h-4 w-4 ${
                                  i < review.rating ? "text-yellow-400 fill-yellow-400" : "text-muted-foreground"
                                }`}
                              />
                            ))}
                        </div>
                        <p className="text-sm">{review.comment}</p>
                      </div>
                    </div>
                    {review.type === "received" && (
                      <div className="flex justify-end mt-4">
                        <Button variant="outline" size="sm">
                          <ThumbsUp className="mr-2 h-4 w-4" />
                          Say Thanks
                        </Button>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        </Tabs>
      </CardContent>
      <CardFooter>
        <Dialog>
          <DialogTrigger asChild>
            <Button className="w-full">
              <MessageSquare className="mr-2 h-4 w-4" />
              Write a Review
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Write a Review</DialogTitle>
              <DialogDescription>Share your experience working with a brand or influencer</DialogDescription>
            </DialogHeader>
            <div className="py-4 space-y-4">
              <div>
                <label className="text-sm font-medium">Who are you reviewing?</label>
                <select className="w-full mt-1 rounded-md border border-input bg-background px-3 py-2">
                  <option value="">Select a collaborator</option>
                  <option value="1">TechGadgets</option>
                  <option value="2">FashionBrand</option>
                  <option value="3">BeautyCompany</option>
                </select>
              </div>
              <div>
                <label className="text-sm font-medium">Rating</label>
                <div className="flex items-center mt-1">
                  {Array(5)
                    .fill(0)
                    .map((_, i) => (
                      <Star key={i} className="h-6 w-6 cursor-pointer text-muted-foreground" />
                    ))}
                </div>
              </div>
              <div>
                <label className="text-sm font-medium">Your Review</label>
                <Textarea
                  placeholder="Share your experience working with this collaborator..."
                  className="mt-1"
                  rows={4}
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline">Cancel</Button>
              <Button>Submit Review</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </CardFooter>
    </Card>
  )
}
