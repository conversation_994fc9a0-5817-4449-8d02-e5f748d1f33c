"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { ResponsiveContainer, LineChart, Line, BarChart, Bar, XAxis, YAxis, CartesianGrid, Legend } from "recharts"
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/button"
import { Calculator } from "lucide-react"

interface ROICalculatorProps {
  compact?: boolean
}

export function ROICalculator({ compact = false }: ROICalculatorProps) {
  const [campaignBudget, setCampaignBudget] = useState("5000")
  const [conversionRate, setConversionRate] = useState("15")
  const [averageOrderValue, setAverageOrderValue] = useState("120")

  // Calculate ROI based on inputs
  const budget = Number.parseFloat(campaignBudget) || 0
  const rate = Number.parseFloat(conversionRate) || 0
  const aov = Number.parseFloat(averageOrderValue) || 0

  const impressions = budget * 200 // Assuming $1 = 200 impressions
  const clicks = impressions * 0.05 // Assuming 5% CTR
  const conversions = clicks * (rate / 100)
  const revenue = conversions * aov
  const roi = budget > 0 ? ((revenue - budget) / budget) * 100 : 0

  // Mock data for ROI over time
  const roiData = [
    { month: "Jan", roi: 280 },
    { month: "Feb", roi: 300 },
    { month: "Mar", roi: 320 },
    { month: "Apr", roi: 280 },
    { month: "May", roi: 300 },
    { month: "Jun", roi: 320 },
  ]

  // Mock data for campaign comparison
  const campaignComparisonData = [
    { name: "Summer Collection", cost: 5000, revenue: 21000, roi: 320 },
    { name: "Product Launch", cost: 4000, revenue: 15200, roi: 280 },
    { name: "Tech Review", cost: 3000, revenue: 10500, roi: 250 },
    { name: "Fitness Challenge", cost: 3500, revenue: 13300, roi: 280 },
    { name: "Holiday Special", cost: 6000, revenue: 25800, roi: 330 },
  ]

  if (compact) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>ROI Calculator</CardTitle>
          <CardDescription>Calculate return on investment for your campaigns</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="border rounded-lg p-3">
                <div className="text-sm text-muted-foreground">Average ROI</div>
                <div className="text-2xl font-bold">320%</div>
              </div>
              <div className="border rounded-lg p-3">
                <div className="text-sm text-muted-foreground">Revenue Generated</div>
                <div className="text-2xl font-bold">$85.5K</div>
              </div>
            </div>
            <div className="h-[150px]">
              <ChartContainer
                config={{
                  roi: {
                    label: "ROI (%)",
                    color: "hsl(var(--chart-1))",
                  },
                }}
              >
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={roiData}>
                    <XAxis dataKey="month" />
                    <YAxis />
                    <ChartTooltip content={<ChartTooltipContent />} />
                    <Line type="monotone" dataKey="roi" stroke="var(--color-roi)" strokeWidth={2} />
                  </LineChart>
                </ResponsiveContainer>
              </ChartContainer>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>ROI Calculator & Analysis</CardTitle>
        <CardDescription>Calculate and analyze return on investment for your campaigns</CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="calculator" className="space-y-4">
          <TabsList>
            <TabsTrigger value="calculator">Calculator</TabsTrigger>
            <TabsTrigger value="trends">ROI Trends</TabsTrigger>
            <TabsTrigger value="comparison">Campaign Comparison</TabsTrigger>
          </TabsList>
          <TabsContent value="calculator">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="campaign-budget">Campaign Budget ($)</Label>
                  <Input
                    id="campaign-budget"
                    type="number"
                    value={campaignBudget}
                    onChange={(e) => setCampaignBudget(e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="conversion-rate">Conversion Rate (%)</Label>
                  <Input
                    id="conversion-rate"
                    type="number"
                    value={conversionRate}
                    onChange={(e) => setConversionRate(e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="average-order">Average Order Value ($)</Label>
                  <Input
                    id="average-order"
                    type="number"
                    value={averageOrderValue}
                    onChange={(e) => setAverageOrderValue(e.target.value)}
                  />
                </div>
                <Button className="w-full">
                  <Calculator className="mr-2 h-4 w-4" />
                  Calculate ROI
                </Button>
              </div>
              <div className="space-y-4">
                <div className="border rounded-lg p-4">
                  <h3 className="text-lg font-medium mb-4">ROI Results</h3>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Estimated Impressions:</span>
                      <span className="font-medium">{impressions.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Estimated Clicks:</span>
                      <span className="font-medium">{clicks.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Estimated Conversions:</span>
                      <span className="font-medium">{conversions.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Estimated Revenue:</span>
                      <span className="font-medium">${revenue.toLocaleString()}</span>
                    </div>
                    <div className="border-t pt-2 mt-2">
                      <div className="flex justify-between">
                        <span className="font-medium">ROI:</span>
                        <span className="font-bold text-xl">{roi.toFixed(1)}%</span>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="border rounded-lg p-4">
                  <h3 className="text-sm font-medium mb-2">ROI Insights</h3>
                  <ul className="list-disc pl-5 space-y-1 text-sm">
                    <li>
                      Your estimated ROI is{" "}
                      {roi > 300 ? "excellent" : roi > 200 ? "very good" : roi > 100 ? "good" : "below average"}
                    </li>
                    <li>The industry average ROI for influencer marketing is around 250%</li>
                    <li>Increasing your conversion rate by 1% could improve your ROI by approximately 20%</li>
                    <li>Consider optimizing your landing pages to improve conversion rates</li>
                  </ul>
                </div>
              </div>
            </div>
          </TabsContent>
          <TabsContent value="trends" className="h-[400px]">
            <ChartContainer
              config={{
                roi: {
                  label: "ROI (%)",
                  color: "hsl(var(--chart-1))",
                },
              }}
            >
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={roiData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <ChartTooltip content={<ChartTooltipContent />} />
                  <Legend />
                  <Line type="monotone" dataKey="roi" stroke="var(--color-roi)" strokeWidth={2} />
                </LineChart>
              </ResponsiveContainer>
            </ChartContainer>
          </TabsContent>
          <TabsContent value="comparison" className="h-[400px]">
            <ChartContainer
              config={{
                cost: {
                  label: "Cost ($)",
                  color: "hsl(var(--chart-1))",
                },
                revenue: {
                  label: "Revenue ($)",
                  color: "hsl(var(--chart-2))",
                },
                roi: {
                  label: "ROI (%)",
                  color: "hsl(var(--chart-3))",
                },
              }}
            >
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={campaignComparisonData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis yAxisId="left" orientation="left" stroke="var(--color-cost)" />
                  <YAxis yAxisId="right" orientation="right" stroke="var(--color-roi)" />
                  <ChartTooltip content={<ChartTooltipContent />} />
                  <Legend />
                  <Bar yAxisId="left" dataKey="cost" fill="var(--color-cost)" />
                  <Bar yAxisId="left" dataKey="revenue" fill="var(--color-revenue)" />
                  <Bar yAxisId="right" dataKey="roi" fill="var(--color-roi)" />
                </BarChart>
              </ResponsiveContainer>
            </ChartContainer>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
