import type React from "react"
import type { Metada<PERSON> } from "next"
import { Plus_Jakarta_Sans } from "next/font/google"
import "./globals.css"
import "react-day-picker/dist/style.css"
import { ThemeProvider } from "@/components/theme-provider"
import { AuthProvider } from "@/contexts/auth-context"
import { PageTransition } from "@/components/page-transition"

const plusJakartaSans = Plus_Jakarta_Sans({
  subsets: ["latin"],
  variable: "--font-plus-jakarta-sans",
})

export const metadata: Metadata = {
  title: "Influenzy - Connect Brands with Influencers",
  description:
    "The platform that connects micro and macro influencers with brands looking for advertisers, affiliates, and collaborators.",
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={`${plusJakartaSans.variable} font-sans`}>
        <ThemeProvider attribute="class" defaultTheme="system" enableSystem disableTransitionOnChange>
          <AuthProvider>
            <PageTransition>{children}</PageTransition>
          </AuthProvider>
        </ThemeProvider>
      </body>
    </html>
  )
}
