"use client"

import { useRef, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { BarChart3, Users, Zap, Globe, TrendingUp, Shield } from "lucide-react"
import gsap from "gsap"
import { registerGSAP, cleanupScrollTriggers } from "@/lib/gsap"

export function FeaturesSection() {
  const sectionRef = useRef<HTMLElement>(null)
  const headingRef = useRef<HTMLDivElement>(null)
  const cardsRef = useRef<HTMLDivElement>(null)

  const features = [
    {
      icon: <BarChart3 className="h-10 w-10" />,
      title: "Advanced Analytics",
      description: "Get detailed insights into your performance metrics, audience demographics, and engagement rates.",
      gradient: "from-brand-purple to-brand-purple-dark",
      bgGradient: "from-brand-purple/10 to-brand-purple-dark/10",
    },
    {
      icon: <Users className="h-10 w-10" />,
      title: "Smart Matching",
      description:
        "Our AI-powered algorithm connects brands with the perfect influencers based on niche, audience, and performance.",
      gradient: "from-brand-blue to-brand-blue-dark",
      bgGradient: "from-brand-blue/10 to-brand-blue-dark/10",
    },
    {
      icon: <Zap className="h-10 w-10" />,
      title: "Streamlined Campaigns",
      description: "Create, manage, and track your campaigns with our intuitive dashboard and workflow tools.",
      gradient: "from-brand-accent-yellow to-orange-500",
      bgGradient: "from-brand-accent-yellow/10 to-orange-500/10",
    },
    {
      icon: <Globe className="h-10 w-10" />,
      title: "Multi-Platform Support",
      description:
        "Connect and manage all your social media accounts in one place - Instagram, YouTube, TikTok, and Facebook.",
      gradient: "from-brand-accent-cyan to-teal-500",
      bgGradient: "from-brand-accent-cyan/10 to-teal-500/10",
    },
    {
      icon: <TrendingUp className="h-10 w-10" />,
      title: "Growth Insights",
      description: "Track your growth over time and get personalized recommendations to improve your performance.",
      gradient: "from-brand-accent-pink to-rose-500",
      bgGradient: "from-brand-accent-pink/10 to-rose-500/10",
    },
    {
      icon: <Shield className="h-10 w-10" />,
      title: "Secure Payments",
      description:
        "Our secure payment system ensures that both brands and influencers are protected throughout the transaction process.",
      gradient: "from-brand-purple-dark to-indigo-600",
      bgGradient: "from-brand-purple-dark/10 to-indigo-600/10",
    },
  ]

  useEffect(() => {
    registerGSAP()

    // Animate the heading when it comes into view
    gsap.fromTo(
      headingRef.current,
      { opacity: 0, y: 50 },
      {
        opacity: 1,
        y: 0,
        duration: 0.8,
        scrollTrigger: {
          trigger: headingRef.current,
          start: "top 80%",
          end: "bottom 20%",
          toggleActions: "play none none reverse",
        },
      },
    )

    // Animate each feature card with a stagger effect
    const cards = cardsRef.current?.querySelectorAll(".feature-card")
    if (cards) {
      gsap.fromTo(
        cards,
        { opacity: 0, y: 50, scale: 0.95 },
        {
          opacity: 1,
          y: 0,
          scale: 1,
          stagger: 0.1,
          duration: 0.6,
          ease: "power2.out",
          scrollTrigger: {
            trigger: cardsRef.current,
            start: "top 80%",
            end: "bottom 20%",
            toggleActions: "play none none reverse",
          },
        },
      )
    }

    return () => {
      cleanupScrollTriggers()
    }
  }, [])

  return (
    <section id="features" ref={sectionRef} className="py-20 bg-muted/50">
      <div className="container px-4 md:px-6">
        <div ref={headingRef} className="flex flex-col items-center text-center mb-12">
          <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">
            Powerful Features for <span className="gradient-text">Everyone</span>
          </h2>
          <p className="mt-4 text-muted-foreground text-lg max-w-[800px]">
            Whether you're a brand looking to launch campaigns or an influencer growing your audience, our platform
            provides all the tools you need to succeed.
          </p>
        </div>

        <div ref={cardsRef} className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <Card 
              key={index} 
              className="feature-card group relative border-0 shadow-lg hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 bg-gradient-to-br from-background to-muted/50 backdrop-blur-sm overflow-hidden"
            >
              {/* Gradient background */}
              <div className={`absolute inset-0 bg-gradient-to-br ${feature.bgGradient} opacity-0 group-hover:opacity-100 transition-opacity duration-500`} />
              
              {/* Animated border */}
              <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-transparent via-brand-purple/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
              
              <CardHeader className="relative z-10 pb-4">
                <div className={`mb-4 p-3 rounded-xl bg-gradient-to-br ${feature.gradient} text-white w-fit group-hover:scale-110 transition-transform duration-300`}>
                  {feature.icon}
                </div>
                <CardTitle className="text-xl group-hover:text-brand-purple transition-colors duration-300">
                  {feature.title}
                </CardTitle>
              </CardHeader>
              <CardContent className="relative z-10">
                <CardDescription className="text-base leading-relaxed group-hover:text-foreground/80 transition-colors duration-300">
                  {feature.description}
                </CardDescription>
              </CardContent>
              
              {/* Hover effect particles */}
              <div className="absolute top-4 right-4 w-2 h-2 bg-brand-accent-pink rounded-full opacity-0 group-hover:opacity-100 group-hover:animate-ping transition-opacity duration-300" />
              <div className="absolute bottom-4 left-4 w-1 h-1 bg-brand-accent-cyan rounded-full opacity-0 group-hover:opacity-100 group-hover:animate-pulse transition-opacity duration-300 delay-100" />
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}
