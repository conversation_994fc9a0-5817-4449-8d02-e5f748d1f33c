"use client"

import { <PERSON>, Card<PERSON>ontent, CardDescription, Card<PERSON><PERSON>er, Card<PERSON>itle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import {
  ResponsiveContainer,
  LineChart,
  Line,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
} from "recharts"
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"
import { DatePickerWithRange } from "@/components/date-range-picker"
import { useState } from "react"

export function PlatformAnalytics() {
  const [dateRange, setDateRange] = useState({
    from: new Date(new Date().setMonth(new Date().getMonth() - 1)),
    to: new Date(),
  })

  // Mock data for platform analytics
  const userGrowthData = [
    { date: "Jan", influencers: 2500, brands: 1200, total: 3700 },
    { date: "Feb", influencers: 3000, brands: 1400, total: 4400 },
    { date: "Mar", influencers: 3500, brands: 1600, total: 5100 },
    { date: "Apr", influencers: 4200, brands: 1800, total: 6000 },
    { date: "May", influencers: 4800, brands: 2100, total: 6900 },
    { date: "Jun", influencers: 5500, brands: 2400, total: 7900 },
  ]

  const campaignData = [
    { date: "Jan", active: 120, completed: 80, total: 200 },
    { date: "Feb", active: 150, completed: 100, total: 250 },
    { date: "Mar", active: 180, completed: 120, total: 300 },
    { date: "Apr", active: 210, completed: 140, total: 350 },
    { date: "May", active: 250, completed: 170, total: 420 },
    { date: "Jun", active: 300, completed: 200, total: 500 },
  ]

  const revenueData = [
    { date: "Jan", revenue: 15000 },
    { date: "Feb", revenue: 18000 },
    { date: "Mar", revenue: 22000 },
    { date: "Apr", revenue: 25000 },
    { date: "May", revenue: 30000 },
    { date: "Jun", revenue: 35000 },
  ]

  const userTypeData = [
    { name: "Influencers", value: 5500 },
    { name: "Brands", value: 2400 },
    { name: "Admins", value: 50 },
  ]

  const COLORS = ["#6C5CE7", "#0984E3", "#00CEC9", "#FF6B81", "#FDCB6E"]

  return (
    <Card>
      <CardHeader>
        <CardTitle>Platform Analytics</CardTitle>
        <CardDescription>Overview of platform performance and metrics</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="mb-6">
          <DatePickerWithRange date={dateRange} setDate={setDateRange} />
        </div>

        <Tabs defaultValue="users" className="space-y-4">
          <TabsList>
            <TabsTrigger value="users">User Growth</TabsTrigger>
            <TabsTrigger value="campaigns">Campaigns</TabsTrigger>
            <TabsTrigger value="revenue">Revenue</TabsTrigger>
            <TabsTrigger value="engagement">Engagement</TabsTrigger>
          </TabsList>
          <TabsContent value="users" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Total Users</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">7,900</div>
                  <p className="text-xs text-muted-foreground">+14.5% from last month</p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Influencers</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">5,500</div>
                  <p className="text-xs text-muted-foreground">+14.6% from last month</p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Brands</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">2,400</div>
                  <p className="text-xs text-muted-foreground">+14.3% from last month</p>
                </CardContent>
              </Card>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm font-medium">User Growth Trend</CardTitle>
                </CardHeader>
                <CardContent className="h-[300px]">
                  <ChartContainer
                    config={{
                      influencers: {
                        label: "Influencers",
                        color: "hsl(var(--chart-1))",
                      },
                      brands: {
                        label: "Brands",
                        color: "hsl(var(--chart-2))",
                      },
                      total: {
                        label: "Total",
                        color: "hsl(var(--chart-3))",
                      },
                    }}
                  >
                    <ResponsiveContainer width="100%" height="100%">
                      <LineChart data={userGrowthData}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="date" />
                        <YAxis />
                        <ChartTooltip content={<ChartTooltipContent />} />
                        <Legend />
                        <Line type="monotone" dataKey="influencers" stroke="var(--color-influencers)" strokeWidth={2} />
                        <Line type="monotone" dataKey="brands" stroke="var(--color-brands)" strokeWidth={2} />
                        <Line type="monotone" dataKey="total" stroke="var(--color-total)" strokeWidth={2} />
                      </LineChart>
                    </ResponsiveContainer>
                  </ChartContainer>
                </CardContent>
              </Card>
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm font-medium">User Distribution</CardTitle>
                </CardHeader>
                <CardContent className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={userTypeData}
                        cx="50%"
                        cy="50%"
                        innerRadius={60}
                        outerRadius={100}
                        fill="#8884d8"
                        dataKey="value"
                        label={({ name, value }) => `${name}: ${value}`}
                      >
                        {userTypeData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip formatter={(value) => value} />
                    </PieChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
          <TabsContent value="campaigns" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Total Campaigns</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">500</div>
                  <p className="text-xs text-muted-foreground">+19% from last month</p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Active Campaigns</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">300</div>
                  <p className="text-xs text-muted-foreground">+20% from last month</p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Completed Campaigns</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">200</div>
                  <p className="text-xs text-muted-foreground">+17.6% from last month</p>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle className="text-sm font-medium">Campaign Growth Trend</CardTitle>
              </CardHeader>
              <CardContent className="h-[300px]">
                <ChartContainer
                  config={{
                    active: {
                      label: "Active",
                      color: "hsl(var(--chart-1))",
                    },
                    completed: {
                      label: "Completed",
                      color: "hsl(var(--chart-2))",
                    },
                    total: {
                      label: "Total",
                      color: "hsl(var(--chart-3))",
                    },
                  }}
                >
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart data={campaignData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="date" />
                      <YAxis />
                      <ChartTooltip content={<ChartTooltipContent />} />
                      <Legend />
                      <Line type="monotone" dataKey="active" stroke="var(--color-active)" strokeWidth={2} />
                      <Line type="monotone" dataKey="completed" stroke="var(--color-completed)" strokeWidth={2} />
                      <Line type="monotone" dataKey="total" stroke="var(--color-total)" strokeWidth={2} />
                    </LineChart>
                  </ResponsiveContainer>
                </ChartContainer>
              </CardContent>
            </Card>
          </TabsContent>
          <TabsContent value="revenue" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">$145,000</div>
                  <p className="text-xs text-muted-foreground">+16.7% from last month</p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Monthly Revenue</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">$35,000</div>
                  <p className="text-xs text-muted-foreground">+16.7% from last month</p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Average Campaign Value</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">$4,200</div>
                  <p className="text-xs text-muted-foreground">+5% from last month</p>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle className="text-sm font-medium">Revenue Growth</CardTitle>
              </CardHeader>
              <CardContent className="h-[300px]">
                <ChartContainer
                  config={{
                    revenue: {
                      label: "Revenue",
                      color: "hsl(var(--chart-1))",
                    },
                  }}
                >
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={revenueData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="date" />
                      <YAxis />
                      <ChartTooltip content={<ChartTooltipContent />} />
                      <Legend />
                      <Bar dataKey="revenue" fill="var(--color-revenue)" />
                    </BarChart>
                  </ResponsiveContainer>
                </ChartContainer>
              </CardContent>
            </Card>
          </TabsContent>
          <TabsContent value="engagement" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Average Engagement Rate</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">4.8%</div>
                  <p className="text-xs text-muted-foreground">+0.3% from last month</p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Total Impressions</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">12.5M</div>
                  <p className="text-xs text-muted-foreground">+18% from last month</p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Conversion Rate</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">15.2%</div>
                  <p className="text-xs text-muted-foreground">+1.2% from last month</p>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle className="text-sm font-medium">Platform Engagement Metrics</CardTitle>
              </CardHeader>
              <CardContent className="h-[300px]">
                <ChartContainer
                  config={{
                    impressions: {
                      label: "Impressions (K)",
                      color: "hsl(var(--chart-1))",
                    },
                    clicks: {
                      label: "Clicks (K)",
                      color: "hsl(var(--chart-2))",
                    },
                    conversions: {
                      label: "Conversions",
                      color: "hsl(var(--chart-3))",
                    },
                  }}
                >
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart
                      data={[
                        { date: "Jan", impressions: 1500, clicks: 75, conversions: 11.4 },
                        { date: "Feb", impressions: 1800, clicks: 90, conversions: 12.5 },
                        { date: "Mar", impressions: 2200, clicks: 110, conversions: 13.2 },
                        { date: "Apr", impressions: 2500, clicks: 125, conversions: 14.0 },
                        { date: "May", impressions: 3000, clicks: 150, conversions: 14.8 },
                        { date: "Jun", impressions: 3500, clicks: 175, conversions: 15.2 },
                      ]}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="date" />
                      <YAxis />
                      <ChartTooltip content={<ChartTooltipContent />} />
                      <Legend />
                      <Line type="monotone" dataKey="impressions" stroke="var(--color-impressions)" strokeWidth={2} />
                      <Line type="monotone" dataKey="clicks" stroke="var(--color-clicks)" strokeWidth={2} />
                      <Line type="monotone" dataKey="conversions" stroke="var(--color-conversions)" strokeWidth={2} />
                    </LineChart>
                  </ResponsiveContainer>
                </ChartContainer>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
