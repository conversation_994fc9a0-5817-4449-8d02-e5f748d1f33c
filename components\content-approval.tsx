"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Ta<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import { CheckCircle2, XCircle, MessageSquare, Eye, ThumbsUp, ThumbsDown, Clock } from "lucide-react"
import Image from "next/image"

export function ContentApproval() {
  const [activeTab, setActiveTab] = useState("pending")

  // Mock data for content submissions
  const contentSubmissions = [
    {
      id: 1,
      status: "pending",
      influencer: {
        name: "<PERSON>",
        avatar: "/placeholder.svg",
      },
      campaign: "Summer Collection Launch",
      contentType: "Instagram Post",
      preview: "/placeholder.svg?height=400&width=400",
      caption:
        "Loving the new summer collection from @FashionBrand! The colors are perfect for the season. #SummerStyle #FashionBrand #Ad",
      submittedAt: "2023-06-15",
    },
    {
      id: 2,
      status: "approved",
      influencer: {
        name: "Sarah Williams",
        avatar: "/placeholder.svg",
      },
      campaign: "Beauty Product Review",
      contentType: "YouTube Video",
      preview: "/placeholder.svg?height=400&width=400",
      caption:
        "Honest review of the new skincare line from BeautyCompany. Watch the full video! #BeautyReview #Sponsored",
      submittedAt: "2023-06-10",
      approvedAt: "2023-06-12",
    },
    {
      id: 3,
      status: "rejected",
      influencer: {
        name: "Mike Chen",
        avatar: "/placeholder.svg",
      },
      campaign: "Tech Gadget Review",
      contentType: "TikTok Video",
      preview: "/placeholder.svg?height=400&width=400",
      caption: "Check out this amazing new gadget! #TechReview #Sponsored",
      submittedAt: "2023-06-05",
      rejectedAt: "2023-06-07",
      rejectionReason: "The content doesn't highlight the key features we discussed in the brief.",
    },
    {
      id: 4,
      status: "pending",
      influencer: {
        name: "Emily Davis",
        avatar: "/placeholder.svg",
      },
      campaign: "Fitness App Promotion",
      contentType: "Instagram Reel",
      preview: "/placeholder.svg?height=400&width=400",
      caption:
        "Transform your workout routine with the FitnessApp! I've been using it for 2 weeks and already seeing results. #FitnessJourney #Sponsored",
      submittedAt: "2023-06-14",
    },
  ]

  const filteredSubmissions = contentSubmissions.filter((submission) => {
    if (activeTab === "all") return true
    return submission.status === activeTab
  })

  return (
    <Card>
      <CardHeader>
        <CardTitle>Content Approval</CardTitle>
        <CardDescription>Review and approve influencer content submissions</CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="pending" onValueChange={setActiveTab}>
          <TabsList className="mb-4">
            <TabsTrigger value="pending">Pending</TabsTrigger>
            <TabsTrigger value="approved">Approved</TabsTrigger>
            <TabsTrigger value="rejected">Rejected</TabsTrigger>
            <TabsTrigger value="all">All</TabsTrigger>
          </TabsList>

          <div className="space-y-4">
            {filteredSubmissions.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">No {activeTab} submissions found</div>
            ) : (
              filteredSubmissions.map((submission) => (
                <Card key={submission.id}>
                  <CardContent className="p-4">
                    <div className="flex flex-col md:flex-row gap-4">
                      <div className="md:w-1/3">
                        <div className="relative aspect-square rounded-md overflow-hidden">
                          <Image
                            src={submission.preview || "/placeholder.svg"}
                            alt="Content Preview"
                            fill
                            className="object-cover"
                          />
                        </div>
                      </div>
                      <div className="md:w-2/3">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center gap-2">
                            <Avatar className="h-8 w-8">
                              <AvatarImage
                                src={submission.influencer.avatar || "/placeholder.svg"}
                                alt={submission.influencer.name}
                              />
                              <AvatarFallback>{submission.influencer.name.substring(0, 2)}</AvatarFallback>
                            </Avatar>
                            <span className="font-medium">{submission.influencer.name}</span>
                          </div>
                          <Badge
                            variant={
                              submission.status === "pending"
                                ? "outline"
                                : submission.status === "approved"
                                  ? "default"
                                  : "destructive"
                            }
                          >
                            {submission.status.charAt(0).toUpperCase() + submission.status.slice(1)}
                          </Badge>
                        </div>
                        <div className="space-y-2">
                          <div>
                            <p className="text-sm text-muted-foreground">Campaign</p>
                            <p className="font-medium">{submission.campaign}</p>
                          </div>
                          <div>
                            <p className="text-sm text-muted-foreground">Content Type</p>
                            <p>{submission.contentType}</p>
                          </div>
                          <div>
                            <p className="text-sm text-muted-foreground">Caption</p>
                            <p className="text-sm">{submission.caption}</p>
                          </div>
                          <div className="flex items-center gap-4 text-sm text-muted-foreground">
                            <div className="flex items-center">
                              <Clock className="mr-1 h-4 w-4" />
                              Submitted: {new Date(submission.submittedAt).toLocaleDateString()}
                            </div>
                            {submission.status === "approved" && (
                              <div className="flex items-center">
                                <CheckCircle2 className="mr-1 h-4 w-4 text-green-500" />
                                Approved: {new Date(submission.approvedAt).toLocaleDateString()}
                              </div>
                            )}
                            {submission.status === "rejected" && (
                              <div className="flex items-center">
                                <XCircle className="mr-1 h-4 w-4 text-red-500" />
                                Rejected: {new Date(submission.rejectedAt).toLocaleDateString()}
                              </div>
                            )}
                          </div>
                          {submission.status === "rejected" && submission.rejectionReason && (
                            <div>
                              <p className="text-sm text-muted-foreground">Rejection Reason</p>
                              <p className="text-sm text-red-500">{submission.rejectionReason}</p>
                            </div>
                          )}
                        </div>
                        {submission.status === "pending" && (
                          <div className="flex gap-2 mt-4">
                            <Dialog>
                              <DialogTrigger asChild>
                                <Button variant="outline">
                                  <Eye className="mr-2 h-4 w-4" />
                                  Preview
                                </Button>
                              </DialogTrigger>
                              <DialogContent>
                                <DialogHeader>
                                  <DialogTitle>Content Preview</DialogTitle>
                                  <DialogDescription>Review the submitted content</DialogDescription>
                                </DialogHeader>
                                <div className="py-4">
                                  <div className="relative aspect-video rounded-md overflow-hidden mb-4">
                                    <Image
                                      src={submission.preview || "/placeholder.svg"}
                                      alt="Content Preview"
                                      fill
                                      className="object-cover"
                                    />
                                  </div>
                                  <div className="space-y-2">
                                    <div>
                                      <p className="text-sm text-muted-foreground">Caption</p>
                                      <p>{submission.caption}</p>
                                    </div>
                                  </div>
                                </div>
                                <DialogFooter>
                                  <Button variant="outline">Close</Button>
                                </DialogFooter>
                              </DialogContent>
                            </Dialog>
                            <Dialog>
                              <DialogTrigger asChild>
                                <Button variant="destructive">
                                  <ThumbsDown className="mr-2 h-4 w-4" />
                                  Reject
                                </Button>
                              </DialogTrigger>
                              <DialogContent>
                                <DialogHeader>
                                  <DialogTitle>Reject Content</DialogTitle>
                                  <DialogDescription>
                                    Please provide a reason for rejecting this content
                                  </DialogDescription>
                                </DialogHeader>
                                <div className="py-4 space-y-4">
                                  <div>
                                    <label className="text-sm font-medium">Rejection Reason</label>
                                    <Textarea
                                      placeholder="Please explain why you're rejecting this content..."
                                      className="mt-1"
                                      rows={4}
                                    />
                                  </div>
                                  <div>
                                    <label className="text-sm font-medium">Feedback for Improvement</label>
                                    <Textarea
                                      placeholder="Provide constructive feedback for the influencer..."
                                      className="mt-1"
                                      rows={4}
                                    />
                                  </div>
                                </div>
                                <DialogFooter>
                                  <Button variant="outline">Cancel</Button>
                                  <Button variant="destructive">Confirm Rejection</Button>
                                </DialogFooter>
                              </DialogContent>
                            </Dialog>
                            <Button>
                              <ThumbsUp className="mr-2 h-4 w-4" />
                              Approve
                            </Button>
                          </div>
                        )}
                        {submission.status !== "pending" && (
                          <div className="flex gap-2 mt-4">
                            <Button variant="outline">
                              <Eye className="mr-2 h-4 w-4" />
                              View Details
                            </Button>
                            <Button variant="outline">
                              <MessageSquare className="mr-2 h-4 w-4" />
                              Message Influencer
                            </Button>
                          </div>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        </Tabs>
      </CardContent>
    </Card>
  )
}
