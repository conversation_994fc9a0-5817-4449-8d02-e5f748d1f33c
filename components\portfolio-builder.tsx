"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Instagram, Youtube, Twitter, Plus, ImageIcon, Edit, Trash2, Eye } from "lucide-react"
import { TikTok } from "@/components/tiktok"
import Image from "next/image"

export function PortfolioBuilder() {
  const [activeTab, setActiveTab] = useState("all")

  // Mock data for portfolio items
  const portfolioItems = [
    {
      id: 1,
      title: "Summer Fashion Lookbook",
      platform: "instagram",
      type: "post",
      image: "/placeholder.svg?height=400&width=400",
      engagement: "5.2k likes, 120 comments",
      date: "2023-06-15",
    },
    {
      id: 2,
      title: "Tech Review: Latest Smartphone",
      platform: "youtube",
      type: "video",
      image: "/placeholder.svg?height=400&width=400",
      engagement: "15k views, 450 likes",
      date: "2023-06-10",
    },
    {
      id: 3,
      title: "Morning Routine Tips",
      platform: "tiktok",
      type: "video",
      image: "/placeholder.svg?height=400&width=400",
      engagement: "20k views, 1.5k likes",
      date: "2023-06-05",
    },
    {
      id: 4,
      title: "Travel Diary: Weekend Getaway",
      platform: "instagram",
      type: "post",
      image: "/placeholder.svg?height=400&width=400",
      engagement: "4.8k likes, 95 comments",
      date: "2023-05-28",
    },
    {
      id: 5,
      title: "Product Unboxing",
      platform: "youtube",
      type: "video",
      image: "/placeholder.svg?height=400&width=400",
      engagement: "12k views, 380 likes",
      date: "2023-05-20",
    },
    {
      id: 6,
      title: "Fitness Challenge",
      platform: "tiktok",
      type: "video",
      image: "/placeholder.svg?height=400&width=400",
      engagement: "18k views, 1.2k likes",
      date: "2023-05-15",
    },
  ]

  const filteredItems = portfolioItems.filter((item) => {
    if (activeTab === "all") return true
    return item.platform === activeTab
  })

  const getPlatformIcon = (platform: string) => {
    switch (platform) {
      case "instagram":
        return <Instagram className="h-4 w-4" />
      case "youtube":
        return <Youtube className="h-4 w-4" />
      case "tiktok":
        return <TikTok className="h-4 w-4" />
      case "twitter":
        return <Twitter className="h-4 w-4" />
      default:
        return null
    }
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>Portfolio Builder</CardTitle>
          <CardDescription>Showcase your best content to attract brands</CardDescription>
        </div>
        <Dialog>
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Add Content
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Add Portfolio Item</DialogTitle>
              <DialogDescription>Add your best content to showcase to brands</DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div>
                <Label htmlFor="title">Title</Label>
                <Input id="title" placeholder="Enter a title for this content" />
              </div>
              <div>
                <Label htmlFor="platform">Platform</Label>
                <Select>
                  <SelectTrigger id="platform">
                    <SelectValue placeholder="Select platform" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="instagram">Instagram</SelectItem>
                    <SelectItem value="youtube">YouTube</SelectItem>
                    <SelectItem value="tiktok">TikTok</SelectItem>
                    <SelectItem value="twitter">Twitter</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="type">Content Type</Label>
                <Select>
                  <SelectTrigger id="type">
                    <SelectValue placeholder="Select content type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="post">Post</SelectItem>
                    <SelectItem value="video">Video</SelectItem>
                    <SelectItem value="reel">Reel/Story</SelectItem>
                    <SelectItem value="tweet">Tweet</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="url">Content URL</Label>
                <Input id="url" placeholder="https://" />
              </div>
              <div>
                <Label htmlFor="description">Description</Label>
                <Textarea id="description" placeholder="Describe this content" />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="engagement">Engagement</Label>
                  <Input id="engagement" placeholder="e.g., 5.2k likes, 120 comments" />
                </div>
                <div>
                  <Label htmlFor="date">Date Published</Label>
                  <Input id="date" type="date" />
                </div>
              </div>
              <div>
                <Label>Thumbnail</Label>
                <div className="mt-2 border-2 border-dashed rounded-lg p-6 flex flex-col items-center justify-center">
                  <ImageIcon className="h-8 w-8 text-muted-foreground mb-2" />
                  <p className="text-sm text-muted-foreground mb-2">Drag and drop an image, or click to browse</p>
                  <Button variant="outline" size="sm">
                    Upload Image
                  </Button>
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline">Cancel</Button>
              <Button>Add to Portfolio</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="all" onValueChange={setActiveTab}>
          <TabsList className="mb-4">
            <TabsTrigger value="all">All</TabsTrigger>
            <TabsTrigger value="instagram">Instagram</TabsTrigger>
            <TabsTrigger value="youtube">YouTube</TabsTrigger>
            <TabsTrigger value="tiktok">TikTok</TabsTrigger>
            <TabsTrigger value="twitter">Twitter</TabsTrigger>
          </TabsList>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredItems.map((item) => (
              <Card key={item.id} className="overflow-hidden">
                <div className="relative aspect-square">
                  <Image src={item.image || "/placeholder.svg"} alt={item.title} fill className="object-cover" />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent" />
                  <div className="absolute bottom-0 left-0 right-0 p-4">
                    <div className="flex items-center gap-2 text-white mb-1">
                      {getPlatformIcon(item.platform)}
                      <span className="text-xs">{item.platform.charAt(0).toUpperCase() + item.platform.slice(1)}</span>
                    </div>
                    <h3 className="font-medium text-white">{item.title}</h3>
                  </div>
                </div>
                <CardFooter className="flex justify-between p-4">
                  <div className="text-xs text-muted-foreground">{item.engagement}</div>
                  <div className="flex gap-2">
                    <Button variant="ghost" size="icon">
                      <Eye className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="icon">
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="icon">
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </CardFooter>
              </Card>
            ))}
          </div>
        </Tabs>
      </CardContent>
    </Card>
  )
}
