"use client"

import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"
import { ResponsiveContainer, LineChart, Line, XAxis, <PERSON>Axis, CartesianGrid, Legend } from "recharts"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { AnimatedChart } from "./animated-chart"

// Sample data - in a real app, this would come from an API
const campaignData = [
  { name: "<PERSON>", impressions: 125000, clicks: 8500, conversions: 850 },
  { name: "Feb", impressions: 145000, clicks: 9800, conversions: 980 },
  { name: "Mar", impressions: 165000, clicks: 11200, conversions: 1120 },
  { name: "Apr", impressions: 195000, clicks: 13300, conversions: 1330 },
  { name: "May", impressions: 225000, clicks: 15400, conversions: 1540 },
  { name: "<PERSON>", impressions: 265000, clicks: 18200, conversions: 1820 },
]

const campaignList = [
  {
    id: 1,
    name: "Summer Collection",
    status: "Active",
    budget: 15000,
    spent: 8750,
    impressions: 450000,
    clicks: 32000,
    conversions: 3200,
    ctr: 7.1,
    cpc: 0.27,
    roi: 320,
  },
  {
    id: 2,
    name: "New Product Launch",
    status: "Active",
    budget: 25000,
    spent: 12500,
    impressions: 680000,
    clicks: 48000,
    conversions: 4800,
    ctr: 7.0,
    cpc: 0.26,
    roi: 384,
  },
  {
    id: 3,
    name: "Holiday Special",
    status: "Scheduled",
    budget: 20000,
    spent: 0,
    impressions: 0,
    clicks: 0,
    conversions: 0,
    ctr: 0,
    cpc: 0,
    roi: 0,
  },
  {
    id: 4,
    name: "Brand Awareness",
    status: "Active",
    budget: 10000,
    spent: 7500,
    impressions: 520000,
    clicks: 28000,
    conversions: 1400,
    ctr: 5.4,
    cpc: 0.27,
    roi: 187,
  },
  {
    id: 5,
    name: "Spring Collection",
    status: "Completed",
    budget: 12000,
    spent: 12000,
    impressions: 580000,
    clicks: 35000,
    conversions: 3500,
    ctr: 6.0,
    cpc: 0.34,
    roi: 292,
  },
]

interface CampaignPerformanceProps {
  dateRange: { from: Date; to: Date }
  detailed?: boolean
}

export function CampaignPerformance({ dateRange, detailed = false }: CampaignPerformanceProps) {
  // Sample data - in a real app, this would come from an API
  const engagementData = [
    { name: "Jan", value: 4000 },
    { name: "Feb", value: 3000 },
    { name: "Mar", value: 5000 },
    { name: "Apr", value: 2780 },
    { name: "May", value: 1890 },
    { name: "Jun", value: 2390 },
    { name: "Jul", value: 3490 },
  ]

  const reachData = [
    { name: "Jan", value: 10000 },
    { name: "Feb", value: 12000 },
    { name: "Mar", value: 15000 },
    { name: "Apr", value: 25000 },
    { name: "May", value: 22000 },
    { name: "Jun", value: 30000 },
    { name: "Jul", value: 40000 },
  ]

  const conversionData = [
    { name: "Jan", value: 1200 },
    { name: "Feb", value: 1500 },
    { name: "Mar", value: 2000 },
    { name: "Apr", value: 2200 },
    { name: "May", value: 1800 },
    { name: "Jun", value: 2400 },
    { name: "Jul", value: 3000 },
  ]

  const activeCampaigns = campaignList.filter((c) => c.status === "Active")
  const totalBudget = campaignList.reduce((sum, campaign) => sum + campaign.budget, 0)
  const totalSpent = campaignList.reduce((sum, campaign) => sum + campaign.spent, 0)
  const totalImpressions = campaignList.reduce((sum, campaign) => sum + campaign.impressions, 0)
  const totalClicks = campaignList.reduce((sum, campaign) => sum + campaign.clicks, 0)
  const totalConversions = campaignList.reduce((sum, campaign) => sum + campaign.conversions, 0)

  const campaignStatusData = [
    { name: "Active", value: campaignList.filter((c) => c.status === "Active").length },
    { name: "Scheduled", value: campaignList.filter((c) => c.status === "Scheduled").length },
    { name: "Completed", value: campaignList.filter((c) => c.status === "Completed").length },
    { name: "Paused", value: campaignList.filter((c) => c.status === "Paused").length },
  ].filter((item) => item.value > 0)

  if (!detailed) {
    return <AnimatedChart data={engagementData} dataKey="value" stroke="#8884d8" />
  }

  return (
    <div className="space-y-4">
      {detailed && (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Budget</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">${totalBudget.toLocaleString()}</div>
              <div className="mt-2 flex items-center text-xs text-muted-foreground">
                <Progress value={(totalSpent / totalBudget) * 100} className="h-2" />
                <span className="ml-2">{Math.round((totalSpent / totalBudget) * 100)}% spent</span>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Impressions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalImpressions.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">Across {activeCampaigns.length} active campaigns</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Clicks</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalClicks.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">
                CTR: {((totalClicks / totalImpressions) * 100).toFixed(2)}%
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Conversions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalConversions.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">
                Conv. Rate: {((totalConversions / totalClicks) * 100).toFixed(2)}%
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {detailed ? (
        <Tabs defaultValue="engagement" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="engagement">Engagement</TabsTrigger>
            <TabsTrigger value="reach">Reach</TabsTrigger>
            <TabsTrigger value="conversion">Conversion</TabsTrigger>
          </TabsList>
          <TabsContent value="engagement" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Engagement Rate</CardTitle>
                <CardDescription>
                  Average engagement rate across all campaigns from {dateRange.from.toLocaleDateString()} to{" "}
                  {dateRange.to.toLocaleDateString()}
                </CardDescription>
              </CardHeader>
              <CardContent className="h-[400px]">
                <AnimatedChart data={engagementData} dataKey="value" stroke="#8884d8" height={350} />
              </CardContent>
            </Card>
          </TabsContent>
          <TabsContent value="reach" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Audience Reach</CardTitle>
                <CardDescription>
                  Total audience reach across all campaigns from {dateRange.from.toLocaleDateString()} to{" "}
                  {dateRange.to.toLocaleDateString()}
                </CardDescription>
              </CardHeader>
              <CardContent className="h-[400px]">
                <AnimatedChart data={reachData} dataKey="value" stroke="#82ca9d" height={350} />
              </CardContent>
            </Card>
          </TabsContent>
          <TabsContent value="conversion" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Conversion Rate</CardTitle>
                <CardDescription>
                  Average conversion rate across all campaigns from {dateRange.from.toLocaleDateString()} to{" "}
                  {dateRange.to.toLocaleDateString()}
                </CardDescription>
              </CardHeader>
              <CardContent className="h-[400px]">
                <AnimatedChart data={conversionData} dataKey="value" stroke="#ffc658" height={350} />
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      ) : (
        <ChartContainer
          config={{
            impressions: {
              label: "Impressions",
              color: "hsl(var(--chart-1))",
            },
            clicks: {
              label: "Clicks",
              color: "hsl(var(--chart-2))",
            },
            conversions: {
              label: "Conversions",
              color: "hsl(var(--chart-3))",
            },
          }}
          className="h-full"
        >
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={campaignData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <ChartTooltip content={<ChartTooltipContent />} />
              <Legend />
              <Line type="monotone" dataKey="impressions" stroke="var(--color-impressions)" activeDot={{ r: 8 }} />
              <Line type="monotone" dataKey="clicks" stroke="var(--color-clicks)" activeDot={{ r: 8 }} />
              <Line type="monotone" dataKey="conversions" stroke="var(--color-conversions)" activeDot={{ r: 8 }} />
            </LineChart>
          </ResponsiveContainer>
        </ChartContainer>
      )}
    </div>
  )
}
