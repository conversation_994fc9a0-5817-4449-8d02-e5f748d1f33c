"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Search, Filter, MoreHorizontal, UserPlus, Eye, Edit, Ban, Trash2, CheckCircle, XCircle, Building } from "lucide-react"

export function BrandManagement() {
  const [searchTerm, setSearchTerm] = useState("")
  const [activeTab, setActiveTab] = useState("all")

  // Mock data for brands
  const brands = [
    {
      id: 1,
      name: "TechGadgets",
      email: "<EMAIL>",
      avatar: "/placeholder.svg",
      industry: "Technology",
      status: "active",
      verified: true,
      joinDate: "2023-01-05",
      lastActive: "2023-06-19",
      campaigns: 12,
      location: "San Francisco, USA",
    },
    {
      id: 2,
      name: "FashionBrand",
      email: "<EMAIL>",
      avatar: "/placeholder.svg",
      industry: "Fashion",
      status: "active",
      verified: true,
      joinDate: "2023-02-25",
      lastActive: "2023-06-17",
      campaigns: 8,
      location: "New York, USA",
    },
    {
      id: 3,
      name: "BeautyCompany",
      email: "<EMAIL>",
      avatar: "/placeholder.svg",
      industry: "Beauty",
      status: "pending",
      verified: false,
      joinDate: "2023-06-05",
      lastActive: "2023-06-05",
      campaigns: 0,
      location: "Los Angeles, USA",
    },
    {
      id: 4,
      name: "FitnessGear",
      email: "<EMAIL>",
      avatar: "/placeholder.svg",
      industry: "Fitness",
      status: "active",
      verified: true,
      joinDate: "2023-03-15",
      lastActive: "2023-06-18",
      campaigns: 5,
      location: "Chicago, USA",
    },
    {
      id: 5,
      name: "FoodDelivery",
      email: "<EMAIL>",
      avatar: "/placeholder.svg",
      industry: "Food & Beverage",
      status: "suspended",
      verified: true,
      joinDate: "2023-01-20",
      lastActive: "2023-05-10",
      campaigns: 3,
      location: "Austin, USA",
    },
    {
      id: 6,
      name: "TravelAgency",
      email: "<EMAIL>",
      avatar: "/placeholder.svg",
      industry: "Travel",
      status: "active",
      verified: true,
      joinDate: "2023-02-10",
      lastActive: "2023-06-15",
      campaigns: 7,
      location: "Miami, USA",
    },
    {
      id: 7,
      name: "HomeDecor",
      email: "<EMAIL>",
      avatar: "/placeholder.svg",
      industry: "Home & Garden",
      status: "pending",
      verified: false,
      joinDate: "2023-06-01",
      lastActive: "2023-06-01",
      campaigns: 0,
      location: "Seattle, USA",
    },
  ]

  const filteredBrands = brands.filter((brand) => {
    // Filter by search term
    const matchesSearch =
      !searchTerm ||
      brand.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      brand.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      brand.industry.toLowerCase().includes(searchTerm.toLowerCase())

    // Filter by tab
    if (activeTab === "all") return matchesSearch
    if (activeTab === "active") return brand.status === "active" && matchesSearch
    if (activeTab === "pending") return brand.status === "pending" && matchesSearch
    if (activeTab === "suspended") return brand.status === "suspended" && matchesSearch

    return matchesSearch
  })

  return (
    <Card>
      <CardHeader>
        <CardTitle>Brand Management</CardTitle>
        <CardDescription>Manage brand accounts and campaigns</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col md:flex-row gap-4 mb-6">
          <div className="relative flex-1">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search by name, email, or industry..."
              className="pl-8"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <div className="flex gap-2">
            <Select defaultValue="all">
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Industry" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Industries</SelectItem>
                <SelectItem value="technology">Technology</SelectItem>
                <SelectItem value="fashion">Fashion</SelectItem>
                <SelectItem value="beauty">Beauty</SelectItem>
                <SelectItem value="fitness">Fitness</SelectItem>
                <SelectItem value="food">Food & Beverage</SelectItem>
                <SelectItem value="travel">Travel</SelectItem>
              </SelectContent>
            </Select>
            <Button variant="outline" size="icon">
              <Filter className="h-4 w-4" />
            </Button>
            <Dialog>
              <DialogTrigger asChild>
                <Button>
                  <Building className="mr-2 h-4 w-4" />
                  Add Brand
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Add New Brand</DialogTitle>
                  <DialogDescription>Create a new brand account</DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                  <div className="grid grid-cols-4 items-center gap-4">
                    <label htmlFor="name" className="text-right">
                      Brand Name
                    </label>
                    <Input id="name" className="col-span-3" />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <label htmlFor="email" className="text-right">
                      Email
                    </label>
                    <Input id="email" type="email" className="col-span-3" />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <label htmlFor="industry" className="text-right">
                      Industry
                    </label>
                    <Select>
                      <SelectTrigger className="col-span-3">
                        <SelectValue placeholder="Select industry" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="technology">Technology</SelectItem>
                        <SelectItem value="fashion">Fashion</SelectItem>
                        <SelectItem value="beauty">Beauty</SelectItem>
                        <SelectItem value="fitness">Fitness</SelectItem>
                        <SelectItem value="food">Food & Beverage</SelectItem>
                        <SelectItem value="travel">Travel</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <label htmlFor="location" className="text-right">
                      Location
                    </label>
                    <Input id="location" className="col-span-3" />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <div className="text-right">Status</div>
                    <div className="flex items-center space-x-2 col-span-3">
                      <Checkbox id="active" />
                      <label
                        htmlFor="active"
                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        Active
                      </label>
                    </div>
                  </div>
                </div>
                <DialogFooter>
                  <Button variant="outline">Cancel</Button>
                  <Button>Create Brand</Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        <Tabs defaultValue="all" onValueChange={setActiveTab}>
          <TabsList className="mb-4">
            <TabsTrigger value="all">All Brands</TabsTrigger>
            <TabsTrigger value="active">Active</TabsTrigger>
            <TabsTrigger value="pending">Pending</TabsTrigger>
            <TabsTrigger value="suspended">Suspended</TabsTrigger>
          </TabsList>

          <div className="rounded-md border">
            <div className="relative w-full overflow-auto">
              <table className="w-full caption-bottom text-sm">
                <thead>
                  <tr className="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted">
                    <th className="h-12 px-4 text-left align-middle font-medium">Brand</th>
                    <th className="h-12 px-4 text-left align-middle font-medium">Industry</th>
                    <th className="h-12 px-4 text-left align-middle font-medium">Status</th>
                    <th className="h-12 px-4 text-left align-middle font-medium">Campaigns</th>
                    <th className="h-12 px-4 text-left align-middle font-medium">Location</th>
                    <th className="h-12 px-4 text-left align-middle font-medium">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredBrands.length === 0 ? (
                    <tr>
                      <td colSpan={6} className="p-4 text-center text-muted-foreground">
                        No brands found
                      </td>
                    </tr>
                  ) : (
                    filteredBrands.map((brand) => (
                      <tr
                        key={brand.id}
                        className="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"
                      >
                        <td className="p-4">
                          <div className="flex items-center gap-3">
                            <Avatar>
                              <AvatarImage src={brand.avatar || "/placeholder.svg"} alt={brand.name} />
                              <AvatarFallback>{brand.name.substring(0, 2)}</AvatarFallback>
                            </Avatar>
                            <div>
                              <div className="font-medium">{brand.name}</div>
                              <div className="text-sm text-muted-foreground">{brand.email}</div>
                            </div>
                            {brand.verified && (
                              <Badge variant="outline" className="ml-2">
                                Verified
                              </Badge>
                            )}
                          </div>
                        </td>
                        <td className="p-4">
                          <Badge variant="outline" className="bg-brand-blue/10 text-brand-blue">
                            {brand.industry}
                          </Badge>
                        </td>
                        <td className="p-4">
                          <Badge
                            variant={
                              brand.status === "active"
                                ? "default"
                                : brand.status === "pending"
                                  ? "outline"
                                  : "destructive"
                            }
                          >
                            {brand.status.charAt(0).toUpperCase() + brand.status.slice(1)}
                          </Badge>
                        </td>
                        <td className="p-4">{brand.campaigns}</td>
                        <td className="p-4">{brand.location}</td>
                        <td className="p-4">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="icon">
                                <MoreHorizontal className="h-4 w-4" />
                                <span className="sr-only">Actions</span>
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem>
                                <Eye className="mr-2 h-4 w-4" />
                                View Profile
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <Edit className="mr-2 h-4 w-4" />
                                Edit Brand
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              {brand.status === "active" ? (
                                <DropdownMenuItem className="text-destructive">
                                  <Ban className="mr-2 h-4 w-4" />
                                  Suspend Brand
                                </DropdownMenuItem>
                              ) : brand.status === "suspended" ? (
                                <DropdownMenuItem>
                                  <CheckCircle className="mr-2 h-4 w-4" />
                                  Reactivate Brand
                                </DropdownMenuItem>
                              ) : (
                                <>
                                  <DropdownMenuItem>
                                    <CheckCircle className="mr-2 h-4 w-4" />
                                    Approve Brand
                                  </DropdownMenuItem>
                                  <DropdownMenuItem className="text-destructive">
                                    <XCircle className="mr-2 h-4 w-4" />
                                    Reject Brand
                                  </DropdownMenuItem>
                                </>
                              )}
                              <DropdownMenuSeparator />
                              <DropdownMenuItem className="text-destructive">
                                <Trash2 className="mr-2 h-4 w-4" />
                                Delete Brand
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>
          </div>
        </Tabs>
      </CardContent>
    </Card>
  )
}
