"use client"

import { useState } from "react"
import { BrandLayout } from "@/components/brand-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table"
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select"
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Calendar, Edit, Eye, Filter, Plus, Search } from "lucide-react"

export function BrandCampaigns() {
  const [statusFilter, setStatusFilter] = useState("all")
  const [typeFilter, setTypeFilter] = useState("all")
  const [viewMode, setViewMode] = useState<"table" | "card">("table")

  // Mock data for campaigns
  const campaigns = [
    {
      id: 1,
      name: "Summer Collection Launch",
      status: "active",
      type: "Post",
      influencers: 5,
      budget: "$3,000",
      startDate: "2023-06-01",
      endDate: "2023-07-15",
      description: "Promote our new summer collection through Instagram posts and stories. Focus on outdoor settings and lifestyle imagery."
    },
    {
      id: 2,
      name: "Product Review Campaign",
      status: "active",
      type: "Product Review",
      influencers: 3,
      budget: "$2,000",
      startDate: "2023-06-15",
      endDate: "2023-07-30",
      description: "Get honest reviews of our new tech gadget from tech influencers. Looking for detailed video reviews highlighting key features."
    },
    {
      id: 3,
      name: "Holiday Special",
      status: "draft",
      type: "UGC",
      influencers: 0,
      budget: "$5,000",
      startDate: "2023-11-01",
      endDate: "2023-12-25",
      description: "User-generated content campaign for the holiday season. Looking for authentic content showcasing our products as gifts."
    },
    {
      id: 4,
      name: "Brand Awareness",
      status: "completed",
      type: "Story",
      influencers: 10,
      budget: "$4,500",
      startDate: "2023-03-15",
      endDate: "2023-04-30",
      description: "Increase brand awareness through Instagram and TikTok stories. Focus on reaching new audiences and highlighting brand values."
    },
    {
      id: 5,
      name: "Fitness Challenge",
      status: "archived",
      type: "Post",
      influencers: 7,
      budget: "$3,200",
      startDate: "2023-01-10",
      endDate: "2023-02-28",
      description: "Fitness challenge campaign encouraging users to share their fitness journey with our products. Weekly challenges and prizes."
    },
    {
      id: 6,
      name: "Back to School",
      status: "draft",
      type: "UGC",
      influencers: 0,
      budget: "$2,800",
      startDate: "2023-08-01",
      endDate: "2023-09-15",
      description: "Back to school campaign targeting students and parents. Looking for content showcasing our products in educational settings."
    }
  ]

  const filteredCampaigns = campaigns.filter(campaign => {
    if (statusFilter !== "all" && campaign.status !== statusFilter) return false
    if (typeFilter !== "all" && campaign.type !== typeFilter) return false
    return true
  })

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case "active":
        return "default"
      case "draft":
        return "outline"
      case "completed":
        return "secondary"
      case "archived":
        return "destructive"
      default:
        return "outline"
    }
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return new Intl.DateTimeFormat("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
    }).format(date)
  }

  return (
    <BrandLayout>
      <div className="flex flex-col gap-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Campaigns</h1>
            <p className="text-muted-foreground">Manage your influencer marketing campaigns</p>
          </div>
          <Dialog>
            <DialogTrigger asChild>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                Create New Campaign
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Create New Campaign</DialogTitle>
                <DialogDescription>Set up a new influencer marketing campaign</DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="campaign-name">Campaign Name</Label>
                    <Input id="campaign-name" placeholder="Enter campaign name" />
                  </div>
                  <div>
                    <Label htmlFor="campaign-type">Campaign Type</Label>
                    <Select>
                      <SelectTrigger id="campaign-type">
                        <SelectValue placeholder="Select type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="post">Post</SelectItem>
                        <SelectItem value="story">Story</SelectItem>
                        <SelectItem value="ugc">UGC</SelectItem>
                        <SelectItem value="product-review">Product Review</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="start-date">Start Date</Label>
                    <Input id="start-date" type="date" />
                  </div>
                  <div>
                    <Label htmlFor="end-date">End Date</Label>
                    <Input id="end-date" type="date" />
                  </div>
                </div>
                <div>
                  <Label htmlFor="budget">Budget</Label>
                  <Input id="budget" placeholder="Enter budget amount" />
                </div>
                <div>
                  <Label htmlFor="description">Description</Label>
                  <Input id="description" placeholder="Enter campaign description" />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline">Save as Draft</Button>
                <Button>Create Campaign</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>

        <Card>
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle>All Campaigns</CardTitle>
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm" onClick={() => setViewMode("table")} className={viewMode === "table" ? "bg-muted" : ""}>
                  Table View
                </Button>
                <Button variant="outline" size="sm" onClick={() => setViewMode("card")} className={viewMode === "card" ? "bg-muted" : ""}>
                  Card View
                </Button>
              </div>
            </div>
            <CardDescription>Manage and track all your marketing campaigns</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col gap-4">
              <div className="flex flex-wrap gap-4 items-center justify-between">
                <div className="flex flex-wrap gap-4 items-center">
                  <div className="flex items-center gap-2">
                    <Filter className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm font-medium">Filter by:</span>
                  </div>
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger className="w-[150px]">
                      <SelectValue placeholder="Status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Statuses</SelectItem>
                      <SelectItem value="draft">Draft</SelectItem>
                      <SelectItem value="active">Active</SelectItem>
                      <SelectItem value="completed">Completed</SelectItem>
                      <SelectItem value="archived">Archived</SelectItem>
                    </SelectContent>
                  </Select>
                  <Select value={typeFilter} onValueChange={setTypeFilter}>
                    <SelectTrigger className="w-[150px]">
                      <SelectValue placeholder="Type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Types</SelectItem>
                      <SelectItem value="Post">Post</SelectItem>
                      <SelectItem value="Story">Story</SelectItem>
                      <SelectItem value="UGC">UGC</SelectItem>
                      <SelectItem value="Product Review">Product Review</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="relative">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input placeholder="Search campaigns..." className="pl-8 w-[250px]" />
                </div>
              </div>

              {viewMode === "table" ? (
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Campaign Name</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Type</TableHead>
                        <TableHead>Influencers</TableHead>
                        <TableHead>Budget</TableHead>
                        <TableHead>Dates</TableHead>
                        <TableHead>Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredCampaigns.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={7} className="text-center py-6 text-muted-foreground">
                            No campaigns found matching your filters
                          </TableCell>
                        </TableRow>
                      ) : (
                        filteredCampaigns.map((campaign) => (
                          <TableRow key={campaign.id}>
                            <TableCell className="font-medium">{campaign.name}</TableCell>
                            <TableCell>
                              <Badge variant={getStatusBadgeVariant(campaign.status)}>
                                {campaign.status.charAt(0).toUpperCase() + campaign.status.slice(1)}
                              </Badge>
                            </TableCell>
                            <TableCell>{campaign.type}</TableCell>
                            <TableCell>{campaign.influencers}</TableCell>
                            <TableCell>{campaign.budget}</TableCell>
                            <TableCell>
                              <div className="flex items-center gap-1 text-sm">
                                <Calendar className="h-3 w-3 text-muted-foreground" />
                                {formatDate(campaign.startDate)} - {formatDate(campaign.endDate)}
                              </div>
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center gap-2">
                                <Button variant="ghost" size="icon">
                                  <Eye className="h-4 w-4" />
                                </Button>
                                <Button variant="ghost" size="icon">
                                  <Edit className="h-4 w-4" />
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {filteredCampaigns.length === 0 ? (
                    <div className="col-span-full text-center py-6 text-muted-foreground">
                      No campaigns found matching your filters
                    </div>
                  ) : (
                    filteredCampaigns.map((campaign) => (
                      <Card key={campaign.id}>
                        <CardHeader className="pb-2">
                          <div className="flex justify-between items-start">
                            <div>
                              <CardTitle className="text-lg">{campaign.name}</CardTitle>
                              <CardDescription>{campaign.type}</CardDescription>
                            </div>
                            <Badge variant={getStatusBadgeVariant(campaign.status)}>
                              {campaign.status.charAt(0).toUpperCase() + campaign.status.slice(1)}
                            </Badge>
                          </div>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-3">
                            <div className="grid grid-cols-2 gap-2 text-sm">
                              <div>
                                <span className="text-muted-foreground">Influencers:</span>
                                <p className="font-medium">{campaign.influencers}</p>
                              </div>
                              <div>
                                <span className="text-muted-foreground">Budget:</span>
                                <p className="font-medium">{campaign.budget}</p>
                              </div>
                            </div>
                            <div className="text-sm">
                              <span className="text-muted-foreground">Dates:</span>
                              <p className="font-medium">
                                {formatDate(campaign.startDate)} - {formatDate(campaign.endDate)}
                              </p>
                            </div>
                            <div className="flex justify-end gap-2 pt-2">
                              <Button variant="outline" size="sm">
                                <Eye className="mr-1 h-4 w-4" />
                                View
                              </Button>
                              <Button variant="outline" size="sm">
                                <Edit className="mr-1 h-4 w-4" />
                                Edit
                              </Button>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))
                  )}
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </BrandLayout>
  )
}
