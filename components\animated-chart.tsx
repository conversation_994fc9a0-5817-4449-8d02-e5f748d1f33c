"use client"

import { useRef, useEffect } from "react"
import { <PERSON><PERSON>hart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from "recharts"
import gsap from "gsap"

interface AnimatedChartProps {
  data: any[]
  dataKey: string
  stroke?: string
  height?: number
}

export function AnimatedChart({ data, dataKey, stroke = "#8884d8", height = 300 }: AnimatedChartProps) {
  const chartRef = useRef<HTMLDivElement>(null)
  const isAnimated = useRef(false)

  useEffect(() => {
    if (!chartRef.current || isAnimated.current) return

    const paths = chartRef.current.querySelectorAll("path.recharts-line-curve")
    const dots = chartRef.current.querySelectorAll(".recharts-line-dot")

    if (paths.length) {
      // Get the total length of the path
      const pathLength = (paths[0] as SVGPathElement).getTotalLength()

      // Set up the initial state - the path is invisible
      gsap.set(paths, {
        strokeDasharray: pathLength,
        strokeDashoffset: pathLength,
      })

      // Hide dots initially
      gsap.set(dots, { opacity: 0, scale: 0 })

      // Create a timeline for the animation
      const tl = gsap.timeline()

      // Animate the path drawing
      tl.to(paths, {
        strokeDashoffset: 0,
        duration: 2,
        ease: "power3.inOut",
      }).to(
        dots,
        {
          opacity: 1,
          scale: 1,
          stagger: 0.05,
          duration: 0.3,
          ease: "back.out(1.7)",
        },
        "-=0.5",
      )

      isAnimated.current = true
    }
  }, [data])

  return (
    <div ref={chartRef} className="w-full h-full">
      <ResponsiveContainer width="100%" height={height}>
        <LineChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
          <CartesianGrid strokeDasharray="3 3" opacity={0.2} />
          <XAxis dataKey="name" />
          <YAxis />
          <Tooltip />
          <Line
            type="monotone"
            dataKey={dataKey}
            stroke={stroke}
            strokeWidth={3}
            dot={{ r: 4, strokeWidth: 2 }}
            activeDot={{ r: 6, strokeWidth: 2 }}
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  )
}
