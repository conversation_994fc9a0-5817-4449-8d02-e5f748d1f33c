"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  <PERSON>alogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Plus, Users, Eye, ThumbsUp } from "lucide-react"
import Link from "next/link"

interface CampaignOverviewProps {
  limit?: number
}

export function CampaignOverview({ limit }: CampaignOverviewProps) {
  const [activeTab, setActiveTab] = useState("active")

  // Mock data for campaigns
  const campaigns = [
    {
      id: 1,
      title: "Summer Collection Launch",
      status: "active",
      progress: 65,
      budget: "$3,000",
      spent: "$1,950",
      startDate: "2023-06-01",
      endDate: "2023-07-15",
      influencers: 5,
      reach: "85K",
      engagement: "4.5%",
      description:
        "Promote our new summer collection through Instagram posts and stories. Focus on outdoor settings and lifestyle imagery.",
    },
    {
      id: 2,
      title: "Product Review Campaign",
      status: "active",
      progress: 30,
      budget: "$2,000",
      spent: "$600",
      startDate: "2023-06-15",
      endDate: "2023-07-30",
      influencers: 3,
      reach: "40K",
      engagement: "3.8%",
      description:
        "Get honest reviews of our new tech gadget from tech influencers. Looking for detailed video reviews highlighting key features.",
    },
    {
      id: 3,
      title: "Spring Collection",
      status: "completed",
      progress: 100,
      budget: "$2,500",
      spent: "$2,500",
      startDate: "2023-03-01",
      endDate: "2023-04-15",
      influencers: 4,
      reach: "70K",
      engagement: "5.2%",
      description: "Promoted our spring collection through Instagram and TikTok content.",
    },
    {
      id: 4,
      title: "Holiday Special",
      status: "draft",
      progress: 0,
      budget: "$4,000",
      spent: "$0",
      startDate: "",
      endDate: "",
      influencers: 0,
      reach: "0",
      engagement: "0%",
      description: "Upcoming campaign for holiday season promotions.",
    },
  ]

  const filteredCampaigns = campaigns.filter((campaign) => {
    if (activeTab === "all") return true
    return campaign.status === activeTab
  })

  const displayedCampaigns = limit ? filteredCampaigns.slice(0, limit) : filteredCampaigns

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Campaigns</CardTitle>
            <CardDescription>Manage your influencer marketing campaigns</CardDescription>
          </div>
          <Dialog>
            <DialogTrigger asChild>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                New Campaign
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Create New Campaign</DialogTitle>
                <DialogDescription>Set up a new influencer marketing campaign</DialogDescription>
              </DialogHeader>
              {/* Campaign creation form would go here */}
              <div className="py-4">
                <p className="text-center text-muted-foreground">Campaign creation wizard coming soon!</p>
              </div>
              <DialogFooter>
                <Button variant="outline">Cancel</Button>
                <Button>Create Campaign</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="active" onValueChange={setActiveTab}>
          <TabsList className="mb-4">
            <TabsTrigger value="active">Active</TabsTrigger>
            <TabsTrigger value="completed">Completed</TabsTrigger>
            <TabsTrigger value="draft">Drafts</TabsTrigger>
            <TabsTrigger value="all">All</TabsTrigger>
          </TabsList>

          <div className="space-y-4">
            {displayedCampaigns.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">No {activeTab} campaigns found</div>
            ) : (
              displayedCampaigns.map((campaign) => (
                <div key={campaign.id} className="border rounded-lg overflow-hidden">
                  <div className="p-4">
                    <div className="flex flex-col md:flex-row md:items-center justify-between mb-4">
                      <div>
                        <h3 className="text-lg font-medium">{campaign.title}</h3>
                        <div className="flex items-center gap-2 mt-1">
                          <Badge
                            variant={
                              campaign.status === "active"
                                ? "default"
                                : campaign.status === "completed"
                                  ? "secondary"
                                  : "outline"
                            }
                          >
                            {campaign.status.charAt(0).toUpperCase() + campaign.status.slice(1)}
                          </Badge>
                          {campaign.status === "active" && (
                            <p className="text-xs text-muted-foreground">{campaign.progress}% complete</p>
                          )}
                        </div>
                      </div>
                      <div className="flex gap-2 mt-2 md:mt-0">
                        <Button variant="outline" size="sm">
                          View Details
                        </Button>
                        {campaign.status === "active" && <Button size="sm">Manage</Button>}
                        {campaign.status === "draft" && <Button size="sm">Edit</Button>}
                      </div>
                    </div>

                    {campaign.status === "active" && <Progress value={campaign.progress} className="h-2 mb-4" />}

                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <p className="text-muted-foreground">Budget</p>
                        <p className="font-medium">{campaign.budget}</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">Spent</p>
                        <p className="font-medium">{campaign.spent}</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">Timeline</p>
                        <p className="font-medium">
                          {campaign.startDate ? (
                            <>
                              {new Date(campaign.startDate).toLocaleDateString()} -{" "}
                              {new Date(campaign.endDate).toLocaleDateString()}
                            </>
                          ) : (
                            "Not set"
                          )}
                        </p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">Influencers</p>
                        <p className="font-medium">{campaign.influencers}</p>
                      </div>
                    </div>

                    {campaign.status !== "draft" && (
                      <div className="grid grid-cols-3 gap-4 mt-4 p-3 bg-muted/50 rounded-lg">
                        <div className="flex flex-col items-center">
                          <Users className="h-4 w-4 text-muted-foreground mb-1" />
                          <p className="text-xs text-muted-foreground">Influencers</p>
                          <p className="font-medium">{campaign.influencers}</p>
                        </div>
                        <div className="flex flex-col items-center">
                          <Eye className="h-4 w-4 text-muted-foreground mb-1" />
                          <p className="text-xs text-muted-foreground">Reach</p>
                          <p className="font-medium">{campaign.reach}</p>
                        </div>
                        <div className="flex flex-col items-center">
                          <ThumbsUp className="h-4 w-4 text-muted-foreground mb-1" />
                          <p className="text-xs text-muted-foreground">Engagement</p>
                          <p className="font-medium">{campaign.engagement}</p>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              ))
            )}
          </div>
        </Tabs>
      </CardContent>
      {limit && filteredCampaigns.length > limit && (
        <CardFooter>
          <Link href="/brand/campaigns">
            <Button variant="outline" className="w-full">
              View All Campaigns
            </Button>
          </Link>
        </CardFooter>
      )}
    </Card>
  )
}
