"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Textarea } from "@/components/ui/textarea"
import { Search, Filter, MoreHorizontal, Eye, CheckCircle, XCircle, Flag, AlertTriangle } from "lucide-react"
import Image from "next/image"

export function ContentModeration() {
  const [searchTerm, setSearchTerm] = useState("")
  const [activeTab, setActiveTab] = useState("pending")
  const [selectedContent, setSelectedContent] = useState<any | null>(null)
  const [isDialogOpen, setIsDialogOpen] = useState(false)

  // Mock data for content moderation
  const contentItems = [
    {
      id: 1,
      type: "post",
      platform: "instagram",
      preview: "/placeholder.svg?height=400&width=400",
      caption: "Check out this amazing product! #ad #sponsored",
      creator: {
        name: "Alex Johnson",
        avatar: "/placeholder.svg",
        role: "influencer",
      },
      brand: {
        name: "TechGadgets",
        avatar: "/placeholder.svg",
      },
      status: "pending",
      reportCount: 0,
      submittedAt: "2023-06-15",
    },
    {
      id: 2,
      type: "video",
      platform: "youtube",
      preview: "/placeholder.svg?height=400&width=400",
      caption: "Honest review of the new smartphone. Is it worth the hype? #sponsored",
      creator: {
        name: "Sarah Williams",
        avatar: "/placeholder.svg",
        role: "influencer",
      },
      brand: {
        name: "TechGadgets",
        avatar: "/placeholder.svg",
      },
      status: "pending",
      reportCount: 0,
      submittedAt: "2023-06-14",
    },
    {
      id: 3,
      type: "post",
      platform: "instagram",
      preview: "/placeholder.svg?height=400&width=400",
      caption: "Loving this new skincare routine! #beauty #sponsored",
      creator: {
        name: "Emily Davis",
        avatar: "/placeholder.svg",
        role: "influencer",
      },
      brand: {
        name: "BeautyCompany",
        avatar: "/placeholder.svg",
      },
      status: "approved",
      reportCount: 0,
      submittedAt: "2023-06-10",
      approvedAt: "2023-06-12",
    },
    {
      id: 4,
      type: "post",
      platform: "instagram",
      preview: "/placeholder.svg?height=400&width=400",
      caption: "This product is amazing! Buy it now with my discount code: ALEX20 #ad",
      creator: {
        name: "Mike Chen",
        avatar: "/placeholder.svg",
        role: "influencer",
      },
      brand: {
        name: "FashionBrand",
        avatar: "/placeholder.svg",
      },
      status: "rejected",
      reportCount: 0,
      submittedAt: "2023-06-08",
      rejectedAt: "2023-06-09",
      rejectionReason: "Discount code not approved by the brand. Please revise.",
    },
    {
      id: 5,
      type: "post",
      platform: "instagram",
      preview: "/placeholder.svg?height=400&width=400",
      caption: "Check out my new outfit! #fashion #style",
      creator: {
        name: "Alex Johnson",
        avatar: "/placeholder.svg",
        role: "influencer",
      },
      status: "flagged",
      reportCount: 3,
      submittedAt: "2023-06-05",
      reportReason: "Inappropriate content",
    },
  ]

  const filteredContent = contentItems.filter((item) => {
    // Filter by search term
    const matchesSearch =
      !searchTerm ||
      item.caption.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.creator.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (item.brand && item.brand.name.toLowerCase().includes(searchTerm.toLowerCase()))

    // Filter by tab
    if (activeTab === "all") return matchesSearch
    return item.status === activeTab && matchesSearch
  })

  const handleViewContent = (content: any) => {
    setSelectedContent(content)
    setIsDialogOpen(true)
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Content Moderation</CardTitle>
        <CardDescription>Review and moderate user-generated content</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col md:flex-row gap-4 mb-6">
          <div className="relative flex-1">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search content..."
              className="pl-8"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <Button variant="outline" size="icon">
            <Filter className="h-4 w-4" />
          </Button>
        </div>

        <Tabs defaultValue="pending" onValueChange={setActiveTab}>
          <TabsList className="mb-4">
            <TabsTrigger value="pending">Pending Review</TabsTrigger>
            <TabsTrigger value="approved">Approved</TabsTrigger>
            <TabsTrigger value="rejected">Rejected</TabsTrigger>
            <TabsTrigger value="flagged">Flagged</TabsTrigger>
            <TabsTrigger value="all">All Content</TabsTrigger>
          </TabsList>

          <div className="space-y-4">
            {filteredContent.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">No content found</div>
            ) : (
              filteredContent.map((content) => (
                <Card key={content.id}>
                  <CardContent className="p-4">
                    <div className="flex flex-col md:flex-row gap-4">
                      <div className="md:w-1/4">
                        <div className="relative aspect-square rounded-md overflow-hidden">
                          <Image
                            src={content.preview || "/placeholder.svg"}
                            alt="Content Preview"
                            fill
                            className="object-cover"
                          />
                        </div>
                      </div>
                      <div className="md:w-3/4">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center gap-2">
                            <Avatar className="h-8 w-8">
                              <AvatarImage
                                src={content.creator.avatar || "/placeholder.svg"}
                                alt={content.creator.name}
                              />
                              <AvatarFallback>{content.creator.name.substring(0, 2)}</AvatarFallback>
                            </Avatar>
                            <span className="font-medium">{content.creator.name}</span>
                            {content.brand && (
                              <>
                                <span className="text-muted-foreground">for</span>
                                <Avatar className="h-6 w-6">
                                  <AvatarImage
                                    src={content.brand.avatar || "/placeholder.svg"}
                                    alt={content.brand.name}
                                  />
                                  <AvatarFallback>{content.brand.name.substring(0, 2)}</AvatarFallback>
                                </Avatar>
                                <span className="text-sm">{content.brand.name}</span>
                              </>
                            )}
                          </div>
                          <div className="flex items-center gap-2">
                            <Badge
                              variant={
                                content.status === "pending"
                                  ? "outline"
                                  : content.status === "approved"
                                    ? "default"
                                    : content.status === "flagged"
                                      ? "secondary"
                                      : "destructive"
                              }
                            >
                              {content.status.charAt(0).toUpperCase() + content.status.slice(1)}
                            </Badge>
                            {content.reportCount > 0 && (
                              <Badge variant="outline" className="bg-yellow-100 text-yellow-800 border-yellow-300">
                                <AlertTriangle className="mr-1 h-3 w-3" />
                                {content.reportCount} Reports
                              </Badge>
                            )}
                          </div>
                        </div>
                        <div className="space-y-2">
                          <div>
                            <div className="flex gap-2 mb-1">
                              <Badge variant="outline" className="bg-muted">
                                {content.platform.charAt(0).toUpperCase() + content.platform.slice(1)}
                              </Badge>
                              <Badge variant="outline" className="bg-muted">
                                {content.type.charAt(0).toUpperCase() + content.type.slice(1)}
                              </Badge>
                            </div>
                            <p className="text-sm">{content.caption}</p>
                          </div>
                          <div className="text-xs text-muted-foreground">
                            Submitted: {new Date(content.submittedAt).toLocaleDateString()}
                            {content.approvedAt && (
                              <span className="ml-2">
                                Approved: {new Date(content.approvedAt).toLocaleDateString()}
                              </span>
                            )}
                            {content.rejectedAt && (
                              <span className="ml-2">
                                Rejected: {new Date(content.rejectedAt).toLocaleDateString()}
                              </span>
                            )}
                          </div>
                          {content.rejectionReason && (
                            <div className="text-sm text-red-500">
                              <span className="font-medium">Rejection reason:</span> {content.rejectionReason}
                            </div>
                          )}
                          {content.reportReason && (
                            <div className="text-sm text-yellow-600">
                              <span className="font-medium">Report reason:</span> {content.reportReason}
                            </div>
                          )}
                        </div>
                        <div className="flex gap-2 mt-4">
                          <Button variant="outline" size="sm" onClick={() => handleViewContent(content)}>
                            <Eye className="mr-2 h-4 w-4" />
                            View Details
                          </Button>
                          {content.status === "pending" && (
                            <>
                              <Button variant="destructive" size="sm">
                                <XCircle className="mr-2 h-4 w-4" />
                                Reject
                              </Button>
                              <Button size="sm">
                                <CheckCircle className="mr-2 h-4 w-4" />
                                Approve
                              </Button>
                            </>
                          )}
                          {content.status === "flagged" && (
                            <>
                              <Button variant="destructive" size="sm">
                                <XCircle className="mr-2 h-4 w-4" />
                                Remove Content
                              </Button>
                              <Button size="sm">
                                <CheckCircle className="mr-2 h-4 w-4" />
                                Mark as Safe
                              </Button>
                            </>
                          )}
                          {(content.status === "approved" || content.status === "rejected") && (
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="outline" size="sm">
                                  <MoreHorizontal className="h-4 w-4" />
                                  <span className="sr-only">Actions</span>
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                <DropdownMenuItem>
                                  <Flag className="mr-2 h-4 w-4" />
                                  Flag Content
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                {content.status === "approved" ? (
                                  <DropdownMenuItem className="text-destructive">
                                    <XCircle className="mr-2 h-4 w-4" />
                                    Revoke Approval
                                  </DropdownMenuItem>
                                ) : (
                                  <DropdownMenuItem>
                                    <CheckCircle className="mr-2 h-4 w-4" />
                                    Approve Content
                                  </DropdownMenuItem>
                                )}
                              </DropdownMenuContent>
                            </DropdownMenu>
                          )}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        </Tabs>
      </CardContent>

      {selectedContent && (
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogContent className="max-w-3xl">
            <DialogHeader>
              <DialogTitle>Content Details</DialogTitle>
              <DialogDescription>Review content details and moderation options</DialogDescription>
            </DialogHeader>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <div className="relative aspect-square rounded-md overflow-hidden">
                  <Image
                    src={selectedContent.preview || "/placeholder.svg"}
                    alt="Content Preview"
                    fill
                    className="object-cover"
                  />
                </div>
              </div>
              <div className="space-y-4">
                <div>
                  <h3 className="text-sm font-medium">Creator</h3>
                  <div className="flex items-center gap-2 mt-1">
                    <Avatar className="h-8 w-8">
                      <AvatarImage
                        src={selectedContent.creator.avatar || "/placeholder.svg"}
                        alt={selectedContent.creator.name}
                      />
                      <AvatarFallback>{selectedContent.creator.name.substring(0, 2)}</AvatarFallback>
                    </Avatar>
                    <span>{selectedContent.creator.name}</span>
                  </div>
                </div>
                {selectedContent.brand && (
                  <div>
                    <h3 className="text-sm font-medium">Brand</h3>
                    <div className="flex items-center gap-2 mt-1">
                      <Avatar className="h-8 w-8">
                        <AvatarImage
                          src={selectedContent.brand.avatar || "/placeholder.svg"}
                          alt={selectedContent.brand.name}
                        />
                        <AvatarFallback>{selectedContent.brand.name.substring(0, 2)}</AvatarFallback>
                      </Avatar>
                      <span>{selectedContent.brand.name}</span>
                    </div>
                  </div>
                )}
                <div>
                  <h3 className="text-sm font-medium">Caption</h3>
                  <p className="text-sm mt-1">{selectedContent.caption}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium">Details</h3>
                  <div className="grid grid-cols-2 gap-2 mt-1 text-sm">
                    <div>
                      <span className="text-muted-foreground">Platform:</span>{" "}
                      {selectedContent.platform.charAt(0).toUpperCase() + selectedContent.platform.slice(1)}
                    </div>
                    <div>
                      <span className="text-muted-foreground">Type:</span>{" "}
                      {selectedContent.type.charAt(0).toUpperCase() + selectedContent.type.slice(1)}
                    </div>
                    <div>
                      <span className="text-muted-foreground">Status:</span>{" "}
                      {selectedContent.status.charAt(0).toUpperCase() + selectedContent.status.slice(1)}
                    </div>
                    <div>
                      <span className="text-muted-foreground">Submitted:</span>{" "}
                      {new Date(selectedContent.submittedAt).toLocaleDateString()}
                    </div>
                  </div>
                </div>
                {selectedContent.status === "pending" && (
                  <div>
                    <h3 className="text-sm font-medium">Moderation</h3>
                    <div className="space-y-2 mt-1">
                      <Textarea placeholder="Add moderation notes..." rows={3} />
                      <div className="flex gap-2">
                        <Button variant="destructive" className="flex-1">
                          <XCircle className="mr-2 h-4 w-4" />
                          Reject
                        </Button>
                        <Button className="flex-1">
                          <CheckCircle className="mr-2 h-4 w-4" />
                          Approve
                        </Button>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                Close
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </Card>
  )
}
