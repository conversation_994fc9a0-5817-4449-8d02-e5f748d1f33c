"use client"

import type React from "react"

import { useRef, useState } from "react"
import { Card, type CardProps } from "@/components/ui/card"
import gsap from "gsap"

interface AnimatedCardProps extends CardProps {
  hoverEffect?: "tilt" | "scale" | "glow" | "none"
  intensity?: number
}

export function AnimatedCard({
  children,
  className,
  hoverEffect = "tilt",
  intensity = 1,
  ...props
}: AnimatedCardProps) {
  const cardRef = useRef<HTMLDivElement>(null)
  const [isHovering, setIsHovering] = useState(false)

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (hoverEffect === "none" || !cardRef.current) return

    const card = cardRef.current
    const rect = card.getBoundingClientRect()
    const x = e.clientX - rect.left
    const y = e.clientY - rect.top

    const centerX = rect.width / 2
    const centerY = rect.height / 2

    const rotateX = ((y - centerY) / centerY) * 10 * intensity
    const rotateY = ((centerX - x) / centerX) * 10 * intensity

    if (hoverEffect === "tilt") {
      gsap.to(card, {
        rotationX: rotateX,
        rotationY: rotateY,
        duration: 0.5,
        ease: "power2.out",
      })
    } else if (hoverEffect === "scale") {
      gsap.to(card, {
        scale: 1.03,
        duration: 0.3,
        ease: "power2.out",
      })
    } else if (hoverEffect === "glow") {
      const glowX = (x / rect.width) * 100
      const glowY = (y / rect.height) * 100

      gsap.to(card, {
        boxShadow: `0 0 20px 5px rgba(var(--card-glow-color, 120, 85, 255), 0.3)`,
        background: `radial-gradient(circle at ${glowX}% ${glowY}%, rgba(var(--card-glow-color, 120, 85, 255), 0.1) 0%, transparent 50%)`,
        duration: 0.3,
      })
    }
  }

  const handleMouseLeave = () => {
    if (hoverEffect === "none" || !cardRef.current) return

    setIsHovering(false)

    gsap.to(cardRef.current, {
      rotationX: 0,
      rotationY: 0,
      scale: 1,
      boxShadow: "none",
      background: "none",
      duration: 0.5,
      ease: "power2.out",
    })
  }

  const handleMouseEnter = () => {
    setIsHovering(true)
  }

  return (
    <Card
      ref={cardRef}
      className={`transform-gpu ${className}`}
      onMouseMove={handleMouseMove}
      onMouseLeave={handleMouseLeave}
      onMouseEnter={handleMouseEnter}
      style={{
        transformStyle: "preserve-3d",
        perspective: "1000px",
      }}
      {...props}
    >
      {children}
    </Card>
  )
}
