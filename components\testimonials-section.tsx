import { Card, CardContent } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Star } from "lucide-react"

export function TestimonialsSection() {
  const testimonials = [
    {
      quote:
        "Influenzy has completely transformed how I find and work with brands. The analytics dashboard gives me insights I never had before!",
      author: "<PERSON>",
      role: "Lifestyle Influencer",
      avatar: "/placeholder.svg?height=40&width=40",
      rating: 5,
    },
    {
      quote:
        "As a brand manager, finding the right influencers used to take weeks. Now I can discover perfect matches for our campaigns in minutes.",
      author: "<PERSON>",
      role: "Marketing Director at FashionCo",
      avatar: "/placeholder.svg?height=40&width=40",
      rating: 5,
    },
    {
      quote:
        "The campaign management tools are intuitive and powerful. I can track everything from initial outreach to final content delivery.",
      author: "<PERSON>",
      role: "Content Creator",
      avatar: "/placeholder.svg?height=40&width=40",
      rating: 4,
    },
    {
      quote:
        "We've increased our ROI by 300% since switching to Influenzy for our influencer marketing campaigns. The data doesn't lie!",
      author: "<PERSON>",
      role: "CMO at TechStart",
      avatar: "/placeholder.svg?height=40&width=40",
      rating: 5,
    },
  ]

  return (
    <section id="testimonials" className="py-20 bg-muted/50">
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center text-center mb-12">
          <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">
            What Our <span className="gradient-text">Users Say</span>
          </h2>
          <p className="mt-4 text-muted-foreground text-lg max-w-[800px]">
            Don't just take our word for it. Here's what influencers and brands have to say about their experience with
            Influenzy.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {testimonials.map((testimonial, index) => (
            <Card key={index} className="border-0 shadow-md">
              <CardContent className="p-6">
                <div className="flex mb-4">
                  {Array(testimonial.rating)
                    .fill(0)
                    .map((_, i) => (
                      <Star key={i} className="h-5 w-5 fill-brand-accent-yellow text-brand-accent-yellow" />
                    ))}
                  {Array(5 - testimonial.rating)
                    .fill(0)
                    .map((_, i) => (
                      <Star key={`empty-${i}`} className="h-5 w-5 text-muted-foreground" />
                    ))}
                </div>
                <p className="mb-6 italic">&ldquo;{testimonial.quote}&rdquo;</p>
                <div className="flex items-center gap-4">
                  <Avatar>
                    <AvatarImage src={testimonial.avatar || "/placeholder.svg"} alt={testimonial.author} />
                    <AvatarFallback>
                      {testimonial.author
                        .split(" ")
                        .map((n) => n[0])
                        .join("")}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="font-medium">{testimonial.author}</p>
                    <p className="text-sm text-muted-foreground">{testimonial.role}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}
