"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Search, Filter, MoreHorizontal, UserPlus, Eye, Edit, Ban, Trash2, CheckCircle, XCircle, Instagram, Youtube } from "lucide-react"
import { TikTok } from "@/components/tiktok"

export function CreatorManagement() {
  const [searchTerm, setSearchTerm] = useState("")
  const [activeTab, setActiveTab] = useState("all")

  // Mock data for creators
  const creators = [
    {
      id: 1,
      name: "Alex Johnson",
      username: "@alexjohnson",
      email: "<EMAIL>",
      avatar: "/placeholder.svg",
      category: "Fashion",
      status: "active",
      verified: true,
      joinDate: "2023-01-15",
      lastActive: "2023-06-20",
      followers: {
        instagram: "120K",
        tiktok: "85K",
        youtube: "50K"
      },
      location: "New York, USA",
      engagementRate: "3.2%"
    },
    {
      id: 2,
      name: "Sarah Williams",
      username: "@sarahwilliams",
      email: "<EMAIL>",
      avatar: "/placeholder.svg",
      category: "Lifestyle",
      status: "active",
      verified: true,
      joinDate: "2023-02-10",
      lastActive: "2023-06-18",
      followers: {
        instagram: "85K",
        tiktok: "150K",
      },
      location: "Los Angeles, USA",
      engagementRate: "4.5%"
    },
    {
      id: 3,
      name: "Mike Chen",
      username: "@mikechen",
      email: "<EMAIL>",
      avatar: "/placeholder.svg",
      category: "Technology",
      status: "suspended",
      verified: true,
      joinDate: "2023-03-20",
      lastActive: "2023-05-15",
      followers: {
        youtube: "250K",
        instagram: "45K"
      },
      location: "San Francisco, USA",
      engagementRate: "2.8%"
    },
    {
      id: 4,
      name: "Emily Davis",
      username: "@emilydavis",
      email: "<EMAIL>",
      avatar: "/placeholder.svg",
      category: "Beauty",
      status: "pending",
      verified: false,
      joinDate: "2023-06-10",
      lastActive: "2023-06-10",
      followers: {
        instagram: "5K",
        tiktok: "8K"
      },
      location: "Chicago, USA",
      engagementRate: "5.1%"
    },
    {
      id: 5,
      name: "James Wilson",
      username: "@jameswilson",
      email: "<EMAIL>",
      avatar: "/placeholder.svg",
      category: "Fitness",
      status: "active",
      verified: true,
      joinDate: "2023-01-25",
      lastActive: "2023-06-19",
      followers: {
        instagram: "180K",
        youtube: "120K"
      },
      location: "Miami, USA",
      engagementRate: "3.9%"
    },
    {
      id: 6,
      name: "Olivia Brown",
      username: "@oliviabrown",
      email: "<EMAIL>",
      avatar: "/placeholder.svg",
      category: "Food",
      status: "active",
      verified: true,
      joinDate: "2023-02-15",
      lastActive: "2023-06-17",
      followers: {
        instagram: "95K",
        tiktok: "210K",
        youtube: "75K"
      },
      location: "Austin, USA",
      engagementRate: "4.2%"
    },
    {
      id: 7,
      name: "David Lee",
      username: "@davidlee",
      email: "<EMAIL>",
      avatar: "/placeholder.svg",
      category: "Travel",
      status: "pending",
      verified: false,
      joinDate: "2023-06-05",
      lastActive: "2023-06-05",
      followers: {
        instagram: "12K",
        youtube: "8K"
      },
      location: "Seattle, USA",
      engagementRate: "3.5%"
    },
  ]

  const filteredCreators = creators.filter((creator) => {
    // Filter by search term
    const matchesSearch =
      !searchTerm ||
      creator.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      creator.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
      creator.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      creator.category.toLowerCase().includes(searchTerm.toLowerCase())

    // Filter by tab
    if (activeTab === "all") return matchesSearch
    if (activeTab === "active") return creator.status === "active" && matchesSearch
    if (activeTab === "pending") return creator.status === "pending" && matchesSearch
    if (activeTab === "suspended") return creator.status === "suspended" && matchesSearch

    return matchesSearch
  })

  // Helper function to get total followers across all platforms
  const getTotalFollowers = (followers) => {
    let total = 0
    Object.values(followers).forEach(count => {
      const numericValue = parseInt(count.replace(/[^0-9]/g, ''))
      total += numericValue
    })
    return total >= 1000 ? `${Math.round(total / 1000)}K` : total.toString()
  }

  // Helper function to get platform icons
  const getPlatformIcons = (followers) => {
    return (
      <div className="flex gap-1">
        {followers.instagram && <Instagram className="h-4 w-4 text-pink-500" />}
        {followers.tiktok && <TikTok className="h-4 w-4" />}
        {followers.youtube && <Youtube className="h-4 w-4 text-red-500" />}
      </div>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Creator Management</CardTitle>
        <CardDescription>Manage creator accounts and collaborations</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col md:flex-row gap-4 mb-6">
          <div className="relative flex-1">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search by name, username, or category..."
              className="pl-8"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <div className="flex gap-2">
            <Select defaultValue="all">
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                <SelectItem value="fashion">Fashion</SelectItem>
                <SelectItem value="lifestyle">Lifestyle</SelectItem>
                <SelectItem value="technology">Technology</SelectItem>
                <SelectItem value="beauty">Beauty</SelectItem>
                <SelectItem value="fitness">Fitness</SelectItem>
                <SelectItem value="food">Food</SelectItem>
                <SelectItem value="travel">Travel</SelectItem>
              </SelectContent>
            </Select>
            <Button variant="outline" size="icon">
              <Filter className="h-4 w-4" />
            </Button>
            <Dialog>
              <DialogTrigger asChild>
                <Button>
                  <UserPlus className="mr-2 h-4 w-4" />
                  Add Creator
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Add New Creator</DialogTitle>
                  <DialogDescription>Create a new creator account</DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                  <div className="grid grid-cols-4 items-center gap-4">
                    <label htmlFor="name" className="text-right">
                      Name
                    </label>
                    <Input id="name" className="col-span-3" />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <label htmlFor="username" className="text-right">
                      Username
                    </label>
                    <Input id="username" className="col-span-3" />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <label htmlFor="email" className="text-right">
                      Email
                    </label>
                    <Input id="email" type="email" className="col-span-3" />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <label htmlFor="category" className="text-right">
                      Category
                    </label>
                    <Select>
                      <SelectTrigger className="col-span-3">
                        <SelectValue placeholder="Select category" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="fashion">Fashion</SelectItem>
                        <SelectItem value="lifestyle">Lifestyle</SelectItem>
                        <SelectItem value="technology">Technology</SelectItem>
                        <SelectItem value="beauty">Beauty</SelectItem>
                        <SelectItem value="fitness">Fitness</SelectItem>
                        <SelectItem value="food">Food</SelectItem>
                        <SelectItem value="travel">Travel</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <label htmlFor="location" className="text-right">
                      Location
                    </label>
                    <Input id="location" className="col-span-3" />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <div className="text-right">Status</div>
                    <div className="flex items-center space-x-2 col-span-3">
                      <Checkbox id="active" />
                      <label
                        htmlFor="active"
                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        Active
                      </label>
                    </div>
                  </div>
                </div>
                <DialogFooter>
                  <Button variant="outline">Cancel</Button>
                  <Button>Create Creator</Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        <Tabs defaultValue="all" onValueChange={setActiveTab}>
          <TabsList className="mb-4">
            <TabsTrigger value="all">All Creators</TabsTrigger>
            <TabsTrigger value="active">Active</TabsTrigger>
            <TabsTrigger value="pending">Pending</TabsTrigger>
            <TabsTrigger value="suspended">Suspended</TabsTrigger>
          </TabsList>

          <div className="rounded-md border">
            <div className="relative w-full overflow-auto">
              <table className="w-full caption-bottom text-sm">
                <thead>
                  <tr className="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted">
                    <th className="h-12 px-4 text-left align-middle font-medium">Creator</th>
                    <th className="h-12 px-4 text-left align-middle font-medium">Category</th>
                    <th className="h-12 px-4 text-left align-middle font-medium">Status</th>
                    <th className="h-12 px-4 text-left align-middle font-medium">Platforms</th>
                    <th className="h-12 px-4 text-left align-middle font-medium">Followers</th>
                    <th className="h-12 px-4 text-left align-middle font-medium">Engagement</th>
                    <th className="h-12 px-4 text-left align-middle font-medium">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredCreators.length === 0 ? (
                    <tr>
                      <td colSpan={7} className="p-4 text-center text-muted-foreground">
                        No creators found
                      </td>
                    </tr>
                  ) : (
                    filteredCreators.map((creator) => (
                      <tr
                        key={creator.id}
                        className="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"
                      >
                        <td className="p-4">
                          <div className="flex items-center gap-3">
                            <Avatar>
                              <AvatarImage src={creator.avatar || "/placeholder.svg"} alt={creator.name} />
                              <AvatarFallback>{creator.name.substring(0, 2)}</AvatarFallback>
                            </Avatar>
                            <div>
                              <div className="font-medium">{creator.name}</div>
                              <div className="text-sm text-muted-foreground">{creator.username}</div>
                            </div>
                            {creator.verified && (
                              <Badge variant="outline" className="ml-2">
                                Verified
                              </Badge>
                            )}
                          </div>
                        </td>
                        <td className="p-4">
                          <Badge variant="outline" className="bg-brand-purple/10 text-brand-purple">
                            {creator.category}
                          </Badge>
                        </td>
                        <td className="p-4">
                          <Badge
                            variant={
                              creator.status === "active"
                                ? "default"
                                : creator.status === "pending"
                                  ? "outline"
                                  : "destructive"
                            }
                          >
                            {creator.status.charAt(0).toUpperCase() + creator.status.slice(1)}
                          </Badge>
                        </td>
                        <td className="p-4">
                          {getPlatformIcons(creator.followers)}
                        </td>
                        <td className="p-4">{getTotalFollowers(creator.followers)}</td>
                        <td className="p-4">{creator.engagementRate}</td>
                        <td className="p-4">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="icon">
                                <MoreHorizontal className="h-4 w-4" />
                                <span className="sr-only">Actions</span>
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem>
                                <Eye className="mr-2 h-4 w-4" />
                                View Profile
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <Edit className="mr-2 h-4 w-4" />
                                Edit Creator
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              {creator.status === "active" ? (
                                <DropdownMenuItem className="text-destructive">
                                  <Ban className="mr-2 h-4 w-4" />
                                  Suspend Creator
                                </DropdownMenuItem>
                              ) : creator.status === "suspended" ? (
                                <DropdownMenuItem>
                                  <CheckCircle className="mr-2 h-4 w-4" />
                                  Reactivate Creator
                                </DropdownMenuItem>
                              ) : (
                                <>
                                  <DropdownMenuItem>
                                    <CheckCircle className="mr-2 h-4 w-4" />
                                    Approve Creator
                                  </DropdownMenuItem>
                                  <DropdownMenuItem className="text-destructive">
                                    <XCircle className="mr-2 h-4 w-4" />
                                    Reject Creator
                                  </DropdownMenuItem>
                                </>
                              )}
                              <DropdownMenuSeparator />
                              <DropdownMenuItem className="text-destructive">
                                <Trash2 className="mr-2 h-4 w-4" />
                                Delete Creator
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>
          </div>
        </Tabs>
      </CardContent>
    </Card>
  )
}
