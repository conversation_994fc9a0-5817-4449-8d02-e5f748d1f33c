"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Button } from "@/components/ui/button"
import {
  BarChart3,
  Home,
  Users,
  Settings,
  Shield,
  LogOut,
  Menu,
  X,
  Bell,
  Flag,
  Star,
  FileText,
  DollarSign,
} from "lucide-react"
import { cn } from "@/lib/utils"
import { useState } from "react"

export function AdminSidebar() {
  const pathname = usePathname()
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)

  const routes = [
    {
      label: "Dashboard",
      icon: Home,
      href: "/admin",
      active: pathname === "/admin",
    },
    {
      label: "Brand Management",
      icon: Users,
      href: "/admin/brands",
      active: pathname === "/admin/brands",
    },
    {
      label: "Creator Management",
      icon: Users,
      href: "/admin/creators",
      active: pathname === "/admin/creators",
    },
    {
      label: "Content Moderation",
      icon: Flag,
      href: "/admin/moderation",
      active: pathname === "/admin/moderation",
    },
    {
      label: "Featured Content",
      icon: Star,
      href: "/admin/featured",
      active: pathname === "/admin/featured",
    },
    {
      label: "Platform Analytics",
      icon: BarChart3,
      href: "/admin/analytics",
      active: pathname === "/admin/analytics",
    },
    {
      label: "Reports",
      icon: FileText,
      href: "/admin/reports",
      active: pathname === "/admin/reports",
    },
    {
      label: "Payments",
      icon: DollarSign,
      href: "/admin/payments",
      active: pathname === "/admin/payments",
    },
    {
      label: "Notifications",
      icon: Bell,
      href: "/admin/notifications",
      active: pathname === "/admin/notifications",
    },
    {
      label: "Security",
      icon: Shield,
      href: "/admin/security",
      active: pathname === "/admin/security",
    },
    {
      label: "Settings",
      icon: Settings,
      href: "/admin/settings",
      active: pathname === "/admin/settings",
    },
  ]

  return (
    <>
      <Button
        variant="outline"
        size="icon"
        className="fixed top-4 left-4 z-50 md:hidden"
        onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
      >
        {isMobileMenuOpen ? <X className="h-4 w-4" /> : <Menu className="h-4 w-4" />}
      </Button>

      <div
        className={cn(
          "fixed inset-y-0 left-0 z-40 w-1/5 bg-background border-r transform transition-transform duration-200 ease-in-out md:translate-x-0 overflow-y-auto",
          isMobileMenuOpen ? "translate-x-0" : "-translate-x-full",
        )}
      >
        <div className="flex flex-col h-full">
          <div className="p-6">
            <Link href="/" className="flex items-center gap-2">
              <span className="text-2xl font-bold gradient-text">Influenzy</span>
            </Link>
            <div className="mt-2 text-sm text-muted-foreground">Admin Panel</div>
          </div>

          <div className="flex-1 px-3 py-2">
            <div className="flex flex-col gap-1">
              {routes.map((route) => (
                <Link
                  key={route.href}
                  href={route.href}
                  className={cn(
                    "flex items-center gap-3 rounded-lg px-3 py-2 text-sm transition-colors",
                    route.active
                      ? "bg-primary text-primary-foreground"
                      : "hover:bg-muted text-muted-foreground hover:text-foreground",
                  )}
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  <route.icon className="h-4 w-4" />
                  {route.label}
                </Link>
              ))}
            </div>
          </div>

          <div className="mt-auto p-4 border-t">
            <div className="flex items-center gap-3 mb-4">
              <Avatar>
                <AvatarImage src="/placeholder.svg" />
                <AvatarFallback>AD</AvatarFallback>
              </Avatar>
              <div>
                <p className="text-sm font-medium">Admin User</p>
                <p className="text-xs text-muted-foreground"><EMAIL></p>
              </div>
            </div>
            <Button variant="outline" className="w-full justify-start" size="sm">
              <LogOut className="mr-2 h-4 w-4" />
              Log out
            </Button>
          </div>
        </div>
      </div>
    </>
  )
}
